{% extends "base.html" %}

{% block title %}
تعديل منحة 8 مارس {{ grant.year }} - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit text-warning me-2"></i>
                    تعديل منحة 8 مارس - عيد المرأة {{ grant.year }}
                </h5>
            </div>
            <div class="card-body">
                <!-- معلومات المنحة الحالية -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i>معلومات المنحة الحالية:</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>السنة:</strong> {{ grant.year }}
                        </div>
                        <div class="col-md-3">
                            <strong>عدد المستفيدات:</strong> {{ grant.total_female_workers }}
                        </div>
                        <div class="col-md-3">
                            <strong>المبلغ الحالي:</strong> {{ grant.formatted_grant_amount }}
                        </div>
                        <div class="col-md-3">
                            <strong>المبلغ الإجمالي:</strong> {{ grant.formatted_total_amount }}
                        </div>
                    </div>
                </div>

                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.grant_amount.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.grant_amount(class="form-control", id="grantAmount", step="0.01", min="0") }}
                                    <span class="input-group-text">دج</span>
                                </div>
                                {% if form.grant_amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.grant_amount.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="text-muted">
                                    سيتم تحديث مبلغ جميع المستفيدات تلقائياً
                                </small>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.issue_date.label(class="form-label") }}
                                {{ form.issue_date(class="form-control") }}
                                {% if form.issue_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.issue_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="5", placeholder="ملاحظات إضافية حول المنحة...") }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- معاينة التغييرات -->
                    <div class="card bg-light mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-calculator me-1"></i>معاينة التغييرات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h5 class="text-primary">{{ grant.total_female_workers }}</h5>
                                        <small class="text-muted">عدد المستفيدات</small>
                                        <br><small class="text-info">(لا يتغير)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h5 class="text-success" id="individualAmount">{{ grant.formatted_grant_amount }}</h5>
                                        <small class="text-muted">مبلغ المنحة الفردية</small>
                                        <br><small class="text-warning">(سيتم التحديث)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h5 class="text-danger" id="totalAmount">{{ grant.formatted_total_amount }}</h5>
                                        <small class="text-muted">المبلغ الإجمالي الجديد</small>
                                        <br><small class="text-warning">(سيتم التحديث)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h5 class="text-info" id="difference">0.00 دج</h5>
                                        <small class="text-muted">الفرق في المبلغ الإجمالي</small>
                                        <br><small id="differenceNote" class="text-muted">لا يوجد تغيير</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- تحذير مهم -->
                    <div class="alert alert-warning mt-4">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i>تحذير مهم:</h6>
                        <ul class="mb-0">
                            <li>تغيير مبلغ المنحة سيؤثر على جميع المستفيدات</li>
                            <li>سيتم تحديث تاريخ الاستلام لجميع المستفيدات</li>
                            <li>حالات التوقيع ستبقى كما هي</li>
                            <li>لا يمكن تغيير السنة أو عدد المستفيدات</li>
                        </ul>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('main.womens_day_grant_detail', id=grant.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>إلغاء
                                </a>
                                {{ form.submit(class="btn btn-warning") }}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const grantAmountInput = document.getElementById('grantAmount');
    const individualAmountDisplay = document.getElementById('individualAmount');
    const totalAmountDisplay = document.getElementById('totalAmount');
    const differenceDisplay = document.getElementById('difference');
    const differenceNote = document.getElementById('differenceNote');
    
    const totalFemaleWorkers = {{ grant.total_female_workers }};
    const originalAmount = {{ grant.grant_amount }};
    const originalTotal = {{ grant.total_amount }};
    
    function updateCalculations() {
        const newGrantAmount = parseFloat(grantAmountInput.value) || 0;
        const newTotalAmount = newGrantAmount * totalFemaleWorkers;
        const difference = newTotalAmount - originalTotal;
        
        // تنسيق المبلغ الفردي
        individualAmountDisplay.textContent = newGrantAmount.toLocaleString('ar-DZ', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' دج';
        
        // تنسيق المبلغ الإجمالي
        totalAmountDisplay.textContent = newTotalAmount.toLocaleString('ar-DZ', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' دج';
        
        // تنسيق الفرق
        const absDefference = Math.abs(difference);
        differenceDisplay.textContent = absDefference.toLocaleString('ar-DZ', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' دج';
        
        // تحديث لون ونص الفرق
        if (difference > 0) {
            differenceDisplay.className = 'text-success';
            differenceNote.textContent = 'زيادة في المبلغ';
            differenceNote.className = 'text-success';
        } else if (difference < 0) {
            differenceDisplay.className = 'text-danger';
            differenceNote.textContent = 'نقص في المبلغ';
            differenceNote.className = 'text-danger';
        } else {
            differenceDisplay.className = 'text-info';
            differenceNote.textContent = 'لا يوجد تغيير';
            differenceNote.className = 'text-muted';
        }
    }
    
    // تحديث الحسابات عند تغيير المبلغ
    grantAmountInput.addEventListener('input', updateCalculations);
    
    // تحديث أولي
    updateCalculations();
});
</script>

<style>
.text-pink {
    color: #e91e63 !important;
}

.card-body .card {
    border: 1px solid #dee2e6;
}

.input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
}

#individualAmount, #totalAmount, #difference {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.alert ul {
    padding-left: 1.5rem;
}

.alert li {
    margin-bottom: 0.25rem;
}

small.text-warning, small.text-info {
    font-weight: bold;
}
</style>
{% endblock %}
