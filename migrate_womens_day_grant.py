#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ترحيل قاعدة البيانات لإضافة جداول منحة 8 مارس
"""

import sqlite3
import os
from datetime import datetime

def migrate_womens_day_grant():
    """إضافة جداول منحة 8 مارس"""
    
    # الاتصال بقاعدة البيانات
    db_path = 'instance/workers_committee.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 بدء ترحيل قاعدة البيانات لمنحة 8 مارس...")
        
        # إنشاء جدول منح 8 مارس
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS womens_day_grants (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                year INTEGER NOT NULL UNIQUE,
                grant_amount REAL NOT NULL,
                total_female_workers INTEGER NOT NULL,
                total_amount REAL NOT NULL,
                issue_date DATE NOT NULL,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول المستفيدات من منحة 8 مارس
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS womens_day_grant_recipients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                grant_id INTEGER NOT NULL,
                worker_id INTEGER NOT NULL,
                amount_received REAL NOT NULL,
                received_date DATE NOT NULL,
                signature_status BOOLEAN DEFAULT 0,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (grant_id) REFERENCES womens_day_grants (id) ON DELETE CASCADE,
                FOREIGN KEY (worker_id) REFERENCES workers (id) ON DELETE CASCADE,
                UNIQUE(grant_id, worker_id)
            )
        ''')
        
        # إنشاء فهارس للأداء
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_womens_grants_year ON womens_day_grants(year)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_womens_recipients_grant ON womens_day_grant_recipients(grant_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_womens_recipients_worker ON womens_day_grant_recipients(worker_id)')
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إنشاء جداول منحة 8 مارس بنجاح!")
        
        # التحقق من الجداول المنشأة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%womens%'")
        tables = cursor.fetchall()
        
        print(f"📊 الجداول المنشأة: {[table[0] for table in tables]}")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
        
    finally:
        if conn:
            conn.close()

def check_database_structure():
    """فحص هيكل قاعدة البيانات"""
    
    db_path = 'instance/workers_committee.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n" + "="*50)
        print("📊 فحص هيكل قاعدة البيانات")
        print("="*50)
        
        # عرض جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"الجداول الموجودة ({len(tables)}):")
        for table in tables:
            print(f"  - {table[0]}")
        
        # فحص جدول منح 8 مارس
        if ('womens_day_grants',) in tables:
            cursor.execute("PRAGMA table_info(womens_day_grants)")
            columns = cursor.fetchall()
            
            print(f"\n📋 هيكل جدول womens_day_grants:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        # فحص جدول المستفيدات
        if ('womens_day_grant_recipients',) in tables:
            cursor.execute("PRAGMA table_info(womens_day_grant_recipients)")
            columns = cursor.fetchall()
            
            print(f"\n📋 هيكل جدول womens_day_grant_recipients:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        # عدد العاملات الإناث
        cursor.execute("SELECT COUNT(*) FROM workers WHERE gender = 'أنثى'")
        female_count = cursor.fetchone()[0]
        print(f"\n👩 عدد العاملات الإناث: {female_count}")
        
        # عدد المنح المسجلة
        if ('womens_day_grants',) in tables:
            cursor.execute("SELECT COUNT(*) FROM womens_day_grants")
            grants_count = cursor.fetchone()[0]
            print(f"🎁 عدد المنح المسجلة: {grants_count}")
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 بدء ترحيل قاعدة البيانات لمنحة 8 مارس")
    print("="*60)
    
    # تنفيذ الترحيل
    success = migrate_womens_day_grant()
    
    if success:
        print("\n✅ تم الترحيل بنجاح!")
        
        # فحص النتيجة
        check_database_structure()
        
        print("\n🎉 جاهز لاستخدام نظام منحة 8 مارس!")
        
    else:
        print("\n❌ فشل في الترحيل!")
