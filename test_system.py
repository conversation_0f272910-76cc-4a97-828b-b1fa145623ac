#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ملف تشخيص النظام
"""

import sys
import os

print("=" * 50)
print("🔍 تشخيص نظام الاصطياف")
print("=" * 50)

# 1. تحقق من Python
print(f"✅ إصدار Python: {sys.version}")
print(f"✅ مسار Python: {sys.executable}")

# 2. تحقق من المجلد الحالي
current_dir = os.getcwd()
print(f"✅ المجلد الحالي: {current_dir}")

# 3. تحقق من الملفات
required_files = ['main.py', 'app.py', 'requirements.txt']
missing_files = []

for file in required_files:
    if os.path.exists(file):
        print(f"✅ موجود: {file}")
    else:
        print(f"❌ مفقود: {file}")
        missing_files.append(file)

# 4. تحقق من مجلد app
if os.path.exists('app'):
    print("✅ مجلد app موجود")
    app_files = os.listdir('app')
    print(f"   الملفات: {app_files}")
else:
    print("❌ مجلد app مفقود")

# 5. تحقق من المكتبات
required_modules = ['flask', 'flask_sqlalchemy', 'flask_wtf']
print("\n📦 تحقق من المكتبات:")

for module in required_modules:
    try:
        __import__(module)
        print(f"✅ {module} مثبت")
    except ImportError:
        print(f"❌ {module} غير مثبت")

# 6. محاولة استيراد التطبيق
print("\n🔄 محاولة استيراد التطبيق:")
try:
    sys.path.insert(0, current_dir)
    from app import create_app
    print("✅ تم استيراد create_app بنجاح")
    
    app = create_app()
    print("✅ تم إنشاء التطبيق بنجاح")
    
except Exception as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("🎯 التشخيص مكتمل")
print("=" * 50)

input("\nاضغط Enter للخروج...")
