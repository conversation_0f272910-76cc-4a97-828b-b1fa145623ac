from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, DateField, SelectField, FieldList, FormField, SubmitField, TextAreaField, FloatField
from wtforms.validators import DataRequired, Length, Optional, NumberRange, ValidationError
from wtforms.widgets import TextArea
from datetime import date

class ChildForm(FlaskForm):
    """نموذج بيانات الطفل"""
    name = StringField('اسم الطفل', validators=[Length(min=2, max=100)])
    birth_date = DateField('تاريخ الميلاد')
    gender = SelectField('الجنس',
                        choices=[('', 'اختر...'), ('ذكر', 'ذكر'), ('أنثى', 'أنثى')])
    education_level = SelectField('المستوى الدراسي',
                                 choices=[
                                     ('', 'اختر...'),
                                     ('ابتدائي', 'ابتدائي'),
                                     ('متوسط', 'متوسط'),
                                     ('ثانوي', 'ثانوي'),
                                     ('جامعي', 'جامعي')
                                 ])

class WorkerForm(FlaskForm):
    """نموذج بيانات العامل"""

    # رقم التسجيل (للعرض فقط في التعديل)
    registration_number = StringField('رقم التسجيل', validators=[Optional(), Length(max=20)],
                                    render_kw={'readonly': True, 'class': 'form-control-plaintext'})

    # البيانات الشخصية الأساسية
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=100)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=100)])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    birth_place = StringField('مكان الميلاد', validators=[DataRequired(), Length(min=2, max=200)])
    personal_address = StringField('العنوان الشخصي', validators=[DataRequired(), Length(min=5, max=300)])
    phone = StringField('الهاتف', validators=[DataRequired(), Length(min=10, max=15)])
    gender = SelectField('الجنس',
                        choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')],
                        validators=[DataRequired()])
    marital_status = SelectField('الحالة الاجتماعية',
                                choices=[
                                    ('متزوج', 'متزوج'),
                                    ('عازب', 'عازب'),
                                    ('مطلق', 'مطلق'),
                                    ('أرمل', 'أرمل')
                                ],
                                validators=[DataRequired()])

    # بيانات العمل
    position = StringField('المنصب', validators=[DataRequired(), Length(min=2, max=200)])
    workplace = StringField('مكان العمل', validators=[DataRequired(), Length(min=2, max=300)])

    # معلومات الهوية والضمان الاجتماعي
    social_security_number = StringField('رقم الضمان الاجتماعي', validators=[Optional(), Length(max=20)])
    id_type = SelectField('نوع الهوية',
                         choices=[('', 'اختر نوع الهوية...'),
                                 ('بطاقة التعريف الوطنية', 'بطاقة التعريف الوطنية'),
                                 ('جواز السفر', 'جواز السفر'),
                                 ('رخصة القيادة', 'رخصة القيادة'),
                                 ('بطاقة الإقامة', 'بطاقة الإقامة')],
                         validators=[Optional()])
    national_id_number = StringField('رقم بطاقة التعريف الوطنية', validators=[Optional(), Length(max=20)])
    id_issue_date = DateField('تاريخ صدور الهوية', validators=[Optional()])
    id_issue_place = StringField('مكان صدور الهوية', validators=[Optional(), Length(max=200)])

    # معلومات الحساب البنكي/البريدي
    account_type = SelectField('نوع الحساب',
                              choices=[
                                  ('', 'اختر نوع الحساب...'),
                                  ('بنكي', 'حساب بنكي'),
                                  ('بريدي', 'حساب بريدي')
                              ])
    account_number = StringField('رقم الحساب (20 رقم)',
                                validators=[Length(min=20, max=20, message='يجب أن يكون رقم الحساب مكون من 20 رقم بالضبط')])
    bank_name = SelectField('اسم البنك', choices=[])
    bank_agency = StringField('الوكالة', validators=[Length(max=100)])

    photo = FileField('صورة العامل', validators=[FileAllowed(['jpg', 'png', 'jpeg'], 'الصور فقط!')])

    # قائمة الأطفال
    children = FieldList(FormField(ChildForm), min_entries=0)

    submit = SubmitField('حفظ')

    def __init__(self, *args, **kwargs):
        super(WorkerForm, self).__init__(*args, **kwargs)
        # تحديث خيارات البنوك من قاعدة البيانات
        from app.models import Bank
        banks = Bank.query.filter_by(is_active=True).order_by(Bank.name).all()
        bank_choices = [('', 'اختر البنك...')]
        bank_choices.extend([(bank.name, bank.name) for bank in banks])
        bank_choices.append(('أخرى', 'أخرى'))
        self.bank_name.choices = bank_choices

class BankForm(FlaskForm):
    """نموذج إضافة بنك جديد"""
    bank_name = StringField('اسم البنك', validators=[DataRequired(), Length(min=2, max=100)])
    submit = SubmitField('إضافة البنك')

class ThermalBathReceiptForm(FlaskForm):
    """نموذج وصل الحمام المعدني"""
    worker_id = SelectField('العامل', coerce=int, validators=[DataRequired()])
    issue_date = DateField('تاريخ الإصدار', validators=[DataRequired()], default=date.today)
    total_amount = FloatField('المبلغ الإجمالي (د.ج)', validators=[DataRequired(), NumberRange(min=0)])
    committee_contribution = FloatField('مساهمة اللجنة (د.ج)', validators=[DataRequired(), NumberRange(min=0)])
    treatment_location = StringField('مكان العلاج', validators=[DataRequired(), Length(min=2, max=200)],
                                   default='حمام الصالحين - عين معبد')
    notes = TextAreaField('ملاحظات', validators=[Length(max=500)])
    submit = SubmitField('إصدار الوصل')

    def __init__(self, *args, **kwargs):
        super(ThermalBathReceiptForm, self).__init__(*args, **kwargs)
        # تحديث قائمة العمال مع معلومات إضافية
        from app.models import Worker
        workers = Worker.query.order_by(Worker.last_name, Worker.first_name).all()
        self.worker_id.choices = [(0, 'اختر العامل...')]
        self.worker_id.choices.extend([
            (worker.id, f"{worker.full_name} - {worker.position} - {worker.workplace}")
            for worker in workers
        ])

    def validate_committee_contribution(self, field):
        if self.total_amount.data and field.data > self.total_amount.data:
            raise ValidationError('مساهمة اللجنة لا يمكن أن تكون أكبر من المبلغ الإجمالي')
