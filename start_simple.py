#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("🚀 تشغيل نظام الاصطياف...")

try:
    from flask import Flask
    from app import create_app, db
    
    print("✅ تم استيراد المكتبات")
    
    app = create_app()
    print("✅ تم إنشاء التطبيق")
    
    with app.app_context():
        db.create_all()
        print("✅ تم إنشاء قاعدة البيانات")
    
    print("🌐 التطبيق متاح على: http://127.0.0.1:5000")
    print("⚠️ لا تغلق هذه النافذة!")
    
    app.run(host='127.0.0.1', port=5000, debug=True)
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 جرب: pip install flask flask-sqlalchemy flask-wtf")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    
input("اضغط Enter للخروج...")
