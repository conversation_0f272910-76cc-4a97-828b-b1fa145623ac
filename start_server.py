#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🚀 بدء تشغيل نظام إدارة لجنة الخدمات الاجتماعية...")
    print("=" * 50)

    # استيراد التطبيق
    from app import create_app, db

    print("✅ تم استيراد التطبيق بنجاح")

    # إنشاء التطبيق
    app = create_app()

    print("✅ تم إنشاء التطبيق بنجاح")

    # إنشاء الجداول
    with app.app_context():
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")

    print("=" * 50)
    print("🌐 التطبيق يعمل على: http://localhost:5000")
    print("🌐 أو: http://127.0.0.1:5000")
    print("=" * 50)
    print("⚠️  للإيقاف اضغط Ctrl+C")
    print("=" * 50)

    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5000)

except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")

except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    import traceback
    traceback.print_exc()

finally:
    print("\n👋 تم إيقاف التطبيق")
    input("اضغط Enter للخروج...")