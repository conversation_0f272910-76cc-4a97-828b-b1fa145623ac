<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منحة 8 مارس {{ grant.year }} - لجنة الخدمات الاجتماعية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .page-break { page-break-before: always; }
            body { font-size: 12px; }
            .table { font-size: 11px; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .header-section {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 10px;
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .stats-card {
            background: white;
            border: 2px solid #e91e63;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .signature-box {
            border: 1px solid #ddd;
            height: 60px;
            margin-top: 5px;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .footer-section {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: center;
        }
        
        .text-pink { color: #e91e63 !important; }
        .bg-pink { background-color: #e91e63 !important; }
        
        .table th {
            background-color: #e91e63 !important;
            color: white !important;
            border: none !important;
        }
        
        .table td {
            vertical-align: middle;
            border-color: #dee2e6;
        }
        
        .badge {
            font-size: 0.8em;
        }
        
        .print-date {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- تاريخ الطباعة -->
    <div class="print-date no-print">
        طُبع في: {{ moment().format('YYYY-MM-DD HH:mm') }}
    </div>

    <!-- أزرار التحكم -->
    <div class="container-fluid no-print mb-3">
        <div class="row">
            <div class="col-12 text-center">
                <button onclick="window.print()" class="btn btn-primary me-2">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
                <button onclick="window.close()" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>إغلاق
                </button>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- رأس الصفحة -->
        <div class="header-section">
            <div class="row">
                <div class="col-12">
                    <div class="logo-section">
                        <h2><i class="fas fa-building me-2"></i>لجنة الخدمات الاجتماعية</h2>
                        <h3>ولاية الجلفة</h3>
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                        <h1><i class="fas fa-gift me-2"></i>منحة 8 مارس - عيد المرأة</h1>
                        <h2>سنة {{ grant.year }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-calendar fa-2x text-pink mb-2"></i>
                    <h4>{{ grant.year }}</h4>
                    <p class="mb-0 text-muted">سنة المنحة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-female fa-2x text-pink mb-2"></i>
                    <h4>{{ grant.total_female_workers }}</h4>
                    <p class="mb-0 text-muted">عدد المستفيدات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-money-bill fa-2x text-pink mb-2"></i>
                    <h4>{{ grant.formatted_grant_amount }}</h4>
                    <p class="mb-0 text-muted">مبلغ المنحة الفردية</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-calculator fa-2x text-pink mb-2"></i>
                    <h4>{{ grant.formatted_total_amount }}</h4>
                    <p class="mb-0 text-muted">المبلغ الإجمالي</p>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="stats-card">
                    <h6><i class="fas fa-calendar-alt text-pink me-1"></i>تاريخ صرف المنحة:</h6>
                    <p class="mb-0">{{ grant.issue_date.strftime('%A، %d %B %Y') }}</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stats-card">
                    <h6><i class="fas fa-clock text-pink me-1"></i>تاريخ إنشاء السجل:</h6>
                    <p class="mb-0">{{ grant.created_at.strftime('%A، %d %B %Y - %H:%M') }}</p>
                </div>
            </div>
        </div>

        {% if grant.notes %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="stats-card">
                    <h6><i class="fas fa-sticky-note text-pink me-1"></i>ملاحظات:</h6>
                    <p class="mb-0">{{ grant.notes }}</p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- قائمة المستفيدات -->
        <div class="table-container">
            <h5 class="text-pink mb-3">
                <i class="fas fa-list me-1"></i>
                قائمة المستفيدات من منحة 8 مارس {{ grant.year }}
            </h5>
            
            {% if recipients %}
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th style="width: 5%;">#</th>
                            <th style="width: 10%;">رقم التسجيل</th>
                            <th style="width: 20%;">اسم العاملة</th>
                            <th style="width: 15%;">المنصب</th>
                            <th style="width: 15%;">مكان العمل</th>
                            <th style="width: 10%;">المبلغ</th>
                            <th style="width: 10%;">تاريخ الاستلام</th>
                            <th style="width: 15%;">التوقيع</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for recipient in recipients %}
                        <tr>
                            <td class="text-center">{{ loop.index }}</td>
                            <td class="text-center">
                                <span class="badge bg-dark">{{ recipient.worker.registration_number or 'غير محدد' }}</span>
                            </td>
                            <td><strong>{{ recipient.worker.full_name }}</strong></td>
                            <td>{{ recipient.worker.position }}</td>
                            <td>{{ recipient.worker.workplace }}</td>
                            <td class="text-center">
                                <strong class="text-success">{{ recipient.formatted_amount }}</strong>
                            </td>
                            <td class="text-center">{{ recipient.received_date.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <div class="signature-box">
                                    {% if recipient.signature_status %}
                                        <div class="text-center pt-2">
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>تم التوقيع
                                            </span>
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- ملخص نهائي -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="text-center p-3 bg-light rounded">
                        <h6>إجمالي المستفيدات</h6>
                        <h4 class="text-primary">{{ recipients|length }}</h4>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center p-3 bg-light rounded">
                        <h6>المبلغ الفردي</h6>
                        <h4 class="text-success">{{ grant.formatted_grant_amount }}</h4>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center p-3 bg-light rounded">
                        <h6>المبلغ الإجمالي</h6>
                        <h4 class="text-danger">{{ grant.formatted_total_amount }}</h4>
                    </div>
                </div>
            </div>

            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5 class="text-muted">لا توجد مستفيدات</h5>
            </div>
            {% endif %}
        </div>

        <!-- التوقيعات الرسمية -->
        <div class="footer-section">
            <div class="row">
                <div class="col-md-4 text-center">
                    <h6>رئيس اللجنة</h6>
                    <div class="signature-box mt-3"></div>
                    <p class="mt-2 mb-0">التوقيع والختم</p>
                </div>
                <div class="col-md-4 text-center">
                    <h6>أمين الصندوق</h6>
                    <div class="signature-box mt-3"></div>
                    <p class="mt-2 mb-0">التوقيع والختم</p>
                </div>
                <div class="col-md-4 text-center">
                    <h6>المحاسب</h6>
                    <div class="signature-box mt-3"></div>
                    <p class="mt-2 mb-0">التوقيع والختم</p>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-1"><strong>لجنة الخدمات الاجتماعية - ولاية الجلفة</strong></p>
                    <p class="mb-0 text-muted">
                        طُبع في: {{ moment().format('dddd، DD MMMM YYYY - HH:mm') }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
        
        // تنسيق التاريخ
        function moment() {
            const now = new Date();
            return {
                format: function(format) {
                    const options = { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric',
                        weekday: 'long',
                        hour: '2-digit',
                        minute: '2-digit'
                    };
                    return now.toLocaleDateString('ar-DZ', options);
                }
            };
        }
    </script>
</body>
</html>
