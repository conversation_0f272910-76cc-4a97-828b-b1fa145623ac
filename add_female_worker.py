#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عاملة أنثى للاختبار
"""

import sqlite3
import os
from datetime import datetime, date

def add_female_worker():
    """إضافة عاملة أنثى للاختبار"""
    
    db_path = 'instance/workers_committee.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 إضافة عاملة أنثى للاختبار...")
        
        # التحقق من وجود عاملات إناث
        cursor.execute("SELECT COUNT(*) FROM workers WHERE gender = 'أنثى'")
        female_count = cursor.fetchone()[0]
        
        print(f"📊 عدد العاملات الإناث الحالي: {female_count}")
        
        # إضافة عاملات إناث للاختبار
        female_workers = [
            {
                'registration_number': 'EMP0003',
                'first_name': 'فاطمة',
                'last_name': 'بن علي',
                'birth_date': '1985-03-15',
                'birth_place': 'الجلفة',
                'personal_address': 'حي النصر، الجلفة',
                'phone': '0555123789',
                'gender': 'أنثى',
                'marital_status': 'متزوجة',
                'position': 'معلمة',
                'workplace': 'مدرسة الشهيد محمد بوضياف',
                'social_security_number': '198503151234567',
                'national_id_number': '123456789014',
                'id_type': 'بطاقة التعريف الوطنية',
                'id_issue_date': '2020-01-10',
                'id_issue_place': 'ولاية الجلفة'
            },
            {
                'registration_number': 'EMP0004',
                'first_name': 'عائشة',
                'last_name': 'محمدي',
                'birth_date': '1990-07-22',
                'birth_place': 'الجلفة',
                'personal_address': 'حي السلام، الجلفة',
                'phone': '0666789123',
                'gender': 'أنثى',
                'marital_status': 'عازبة',
                'position': 'ممرضة',
                'workplace': 'المستشفى المحلي',
                'social_security_number': '199007221234568',
                'national_id_number': '123456789015',
                'id_type': 'بطاقة التعريف الوطنية',
                'id_issue_date': '2019-05-15',
                'id_issue_place': 'ولاية الجلفة'
            },
            {
                'registration_number': 'EMP0005',
                'first_name': 'خديجة',
                'last_name': 'العربي',
                'birth_date': '1988-12-08',
                'birth_place': 'الجلفة',
                'personal_address': 'حي الوحدة، الجلفة',
                'phone': '0777456789',
                'gender': 'أنثى',
                'marital_status': 'متزوجة',
                'position': 'إدارية',
                'workplace': 'دائرة الجلفة',
                'social_security_number': '198812081234569',
                'national_id_number': '123456789016',
                'id_type': 'بطاقة التعريف الوطنية',
                'id_issue_date': '2021-02-20',
                'id_issue_place': 'ولاية الجلفة'
            }
        ]
        
        for worker_data in female_workers:
            # التحقق من عدم وجود العاملة مسبقاً
            cursor.execute("SELECT id FROM workers WHERE registration_number = ?", 
                         (worker_data['registration_number'],))
            existing = cursor.fetchone()
            
            if existing:
                print(f"⚠️  العاملة {worker_data['first_name']} {worker_data['last_name']} موجودة مسبقاً")
                continue
            
            # إدراج العاملة
            cursor.execute('''
                INSERT INTO workers (
                    registration_number, first_name, last_name, birth_date, birth_place,
                    personal_address, phone, gender, marital_status, position, workplace,
                    social_security_number, national_id_number, id_type, id_issue_date, id_issue_place,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                worker_data['registration_number'],
                worker_data['first_name'],
                worker_data['last_name'],
                worker_data['birth_date'],
                worker_data['birth_place'],
                worker_data['personal_address'],
                worker_data['phone'],
                worker_data['gender'],
                worker_data['marital_status'],
                worker_data['position'],
                worker_data['workplace'],
                worker_data['social_security_number'],
                worker_data['national_id_number'],
                worker_data['id_type'],
                worker_data['id_issue_date'],
                worker_data['id_issue_place'],
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))
            
            print(f"✅ تم إضافة العاملة: {worker_data['first_name']} {worker_data['last_name']}")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM workers WHERE gender = 'أنثى'")
        new_female_count = cursor.fetchone()[0]
        
        print(f"\n📊 عدد العاملات الإناث الجديد: {new_female_count}")
        print(f"➕ تم إضافة: {new_female_count - female_count} عاملة")
        
        # عرض قائمة العاملات الإناث
        cursor.execute('''
            SELECT registration_number, first_name, last_name, position, workplace 
            FROM workers WHERE gender = 'أنثى' 
            ORDER BY registration_number
        ''')
        
        female_workers_list = cursor.fetchall()
        
        print(f"\n👩 قائمة العاملات الإناث:")
        print("-" * 60)
        for worker in female_workers_list:
            print(f"{worker[0]} - {worker[1]} {worker[2]} - {worker[3]} - {worker[4]}")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 إضافة عاملات إناث للاختبار")
    print("="*50)
    
    success = add_female_worker()
    
    if success:
        print("\n✅ تم إضافة العاملات بنجاح!")
        print("🎉 يمكن الآن اختبار نظام منحة 8 مارس!")
    else:
        print("\n❌ فشل في إضافة العاملات!")
