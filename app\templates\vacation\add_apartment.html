{% extends "base.html" %}

{% block title %}إضافة شقة جديدة - نظام الاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-home"></i>
                        إضافة شقة جديدة
                    </h3>
                </div>
                
                <form method="POST">
                    {{ form.hidden_tag() }}
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.building_id.label(class="form-label") }}
                                    <i class="fas fa-building"></i>
                                    <span class="text-danger">*</span>
                                    {{ form.building_id(class="form-select") }}
                                    {% if form.building_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.building_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    {{ form.floor_number.label(class="form-label") }}
                                    <i class="fas fa-layer-group"></i>
                                    <span class="text-danger">*</span>
                                    {{ form.floor_number(class="form-control", placeholder="مثال: 2") }}
                                    {% if form.floor_number.errors %}
                                        <div class="text-danger">
                                            {% for error in form.floor_number.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    {{ form.apartment_number.label(class="form-label") }}
                                    <i class="fas fa-door-open"></i>
                                    <span class="text-danger">*</span>
                                    {{ form.apartment_number(class="form-control", placeholder="مثال: A01") }}
                                    {% if form.apartment_number.errors %}
                                        <div class="text-danger">
                                            {% for error in form.apartment_number.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.capacity.label(class="form-label") }}
                                    <i class="fas fa-users"></i>
                                    <span class="text-danger">*</span>
                                    {{ form.capacity(class="form-control") }}
                                    {% if form.capacity.errors %}
                                        <div class="text-danger">
                                            {% for error in form.capacity.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.price_per_week.label(class="form-label") }}
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span class="text-danger">*</span>
                                    {{ form.price_per_week(class="form-control", placeholder="مثال: 15000") }}
                                    {% if form.price_per_week.errors %}
                                        <div class="text-danger">
                                            {% for error in form.price_per_week.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-toggle-on"></i>
                                        الحالة
                                    </label>
                                    <div class="form-check form-switch">
                                        {{ form.is_available(class="form-check-input") }}
                                        {{ form.is_available.label(class="form-check-label") }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            <i class="fas fa-info-circle"></i>
                            {{ form.description(class="form-control", rows="4", placeholder="وصف مختصر عن الشقة ومرافقها (غرف النوم، الحمامات، المطبخ، إلخ)...") }}
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.vacation_apartments_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                العودة للقائمة
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle"></i>
                    دليل إضافة الشقق
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> نصائح للترقيم</h6>
                            <ul class="mb-0">
                                <li>استخدم نظام ترقيم واضح (A01, B02, إلخ)</li>
                                <li>رقم الطابق: 0 للأرضي، 1 للأول، إلخ</li>
                                <li>تأكد من عدم تكرار أرقام الشقق</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-dollar-sign"></i> تحديد الأسعار</h6>
                            <ul class="mb-0">
                                <li>السعر محسوب لكل أسبوع (7 أيام)</li>
                                <li>يمكن تعديل الأسعار لاحقاً</li>
                                <li>ضع في الاعتبار السعة والمرافق</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> ملاحظات مهمة</h6>
                            <ul class="mb-0">
                                <li>السعة القصوى 12 شخص</li>
                                <li>يمكن إيقاف الشقة مؤقتاً</li>
                                <li>الوصف يساعد في اختيار الشقة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // التحقق من صحة النموذج
    $('form').on('submit', function(e) {
        var buildingId = $('#building_id').val();
        var floorNumber = parseInt($('#floor_number').val());
        var apartmentNumber = $('#apartment_number').val().trim();
        var capacity = parseInt($('#capacity').val());
        var price = parseFloat($('#price_per_week').val());
        
        if (!buildingId || !apartmentNumber || !capacity || !price) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        if (floorNumber < 0 || floorNumber > 20) {
            e.preventDefault();
            alert('رقم الطابق يجب أن يكون بين 0 و 20');
            return false;
        }
        
        if (capacity < 1 || capacity > 12) {
            e.preventDefault();
            alert('السعة يجب أن تكون بين 1 و 12 شخص');
            return false;
        }
        
        if (price <= 0) {
            e.preventDefault();
            alert('السعر يجب أن يكون أكبر من صفر');
            return false;
        }
    });
    
    // تنسيق رقم الشقة
    $('#apartment_number').on('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    });
    
    // تنسيق السعر
    $('#price_per_week').on('input', function() {
        var value = parseFloat(this.value);
        if (value && value > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
});
</script>
{% endblock %}
