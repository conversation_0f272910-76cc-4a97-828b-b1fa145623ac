{% extends "base.html" %}

{% block title %}إضافة شقة جديدة - نظام الاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-home"></i>
                        إضافة شقة جديدة
                    </h3>
                </div>
                
                <form method="POST">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="building_id" class="form-label">
                                        <i class="fas fa-building"></i>
                                        العمارة <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="building_id" name="building_id" required>
                                        <option value="">اختر العمارة</option>
                                        {% for building in buildings %}
                                            <option value="{{ building.id }}">{{ building.name }} - {{ building.location }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="floor_number" class="form-label">
                                        <i class="fas fa-layer-group"></i>
                                        رقم الطابق <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="floor_number" name="floor_number" 
                                           min="0" max="20" placeholder="مثال: 2" required>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="apartment_number" class="form-label">
                                        <i class="fas fa-door-open"></i>
                                        رقم الشقة <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="apartment_number" name="apartment_number" 
                                           placeholder="مثال: A01" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="capacity" class="form-label">
                                        <i class="fas fa-users"></i>
                                        السعة (عدد الأشخاص) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="capacity" name="capacity" 
                                           min="1" max="12" value="4" required>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="price_per_week" class="form-label">
                                        <i class="fas fa-money-bill-wave"></i>
                                        السعر لكل أسبوع (دج) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="price_per_week" name="price_per_week" 
                                           min="0" step="0.01" placeholder="مثال: 15000" required>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="is_available" class="form-label">
                                        <i class="fas fa-toggle-on"></i>
                                        الحالة
                                    </label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_available" 
                                               name="is_available" checked>
                                        <label class="form-check-label" for="is_available">
                                            متاحة للحجز
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-info-circle"></i>
                                وصف الشقة
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="وصف مختصر عن الشقة ومرافقها (غرف النوم، الحمامات، المطبخ، إلخ)..."></textarea>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.vacation_apartments_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ الشقة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle"></i>
                    دليل إضافة الشقق
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> نصائح للترقيم</h6>
                            <ul class="mb-0">
                                <li>استخدم نظام ترقيم واضح (A01, B02, إلخ)</li>
                                <li>رقم الطابق: 0 للأرضي، 1 للأول، إلخ</li>
                                <li>تأكد من عدم تكرار أرقام الشقق</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-dollar-sign"></i> تحديد الأسعار</h6>
                            <ul class="mb-0">
                                <li>السعر محسوب لكل أسبوع (7 أيام)</li>
                                <li>يمكن تعديل الأسعار لاحقاً</li>
                                <li>ضع في الاعتبار السعة والمرافق</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> ملاحظات مهمة</h6>
                            <ul class="mb-0">
                                <li>السعة القصوى 12 شخص</li>
                                <li>يمكن إيقاف الشقة مؤقتاً</li>
                                <li>الوصف يساعد في اختيار الشقة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // التحقق من صحة النموذج
    $('form').on('submit', function(e) {
        var buildingId = $('#building_id').val();
        var floorNumber = parseInt($('#floor_number').val());
        var apartmentNumber = $('#apartment_number').val().trim();
        var capacity = parseInt($('#capacity').val());
        var price = parseFloat($('#price_per_week').val());
        
        if (!buildingId || !apartmentNumber || !capacity || !price) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        if (floorNumber < 0 || floorNumber > 20) {
            e.preventDefault();
            alert('رقم الطابق يجب أن يكون بين 0 و 20');
            return false;
        }
        
        if (capacity < 1 || capacity > 12) {
            e.preventDefault();
            alert('السعة يجب أن تكون بين 1 و 12 شخص');
            return false;
        }
        
        if (price <= 0) {
            e.preventDefault();
            alert('السعر يجب أن يكون أكبر من صفر');
            return false;
        }
    });
    
    // تنسيق رقم الشقة
    $('#apartment_number').on('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    });
    
    // تنسيق السعر
    $('#price_per_week').on('input', function() {
        var value = parseFloat(this.value);
        if (value && value > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
});
</script>
{% endblock %}
