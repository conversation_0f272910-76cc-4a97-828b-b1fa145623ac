{% extends "base.html" %}

{% block title %}إضافة عمارة جديدة - نظام الاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building"></i>
                        إضافة عمارة جديدة
                    </h3>
                </div>
                
                <form method="POST">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-building"></i>
                                        اسم العمارة <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           placeholder="مثال: عمارة الأسرة الجامعية" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="total_floors" class="form-label">
                                        <i class="fas fa-layer-group"></i>
                                        عدد الطوابق <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="total_floors" name="total_floors" 
                                           min="1" max="20" placeholder="مثال: 5" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="location" class="form-label">
                                <i class="fas fa-map-marker-alt"></i>
                                الموقع <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   placeholder="مثال: سيدي فرج - الجزائر العاصمة" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-info-circle"></i>
                                وصف العمارة
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="وصف مختصر عن العمارة ومرافقها..."></textarea>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.vacation_buildings_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                حفظ العمارة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> نصائح</h6>
                            <ul class="mb-0">
                                <li>اختر اسماً واضحاً ومميزاً للعمارة</li>
                                <li>حدد الموقع بدقة لسهولة الوصول</li>
                                <li>أضف وصفاً شاملاً للمرافق المتاحة</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> بعد الإضافة</h6>
                            <ul class="mb-0">
                                <li>يمكنك إضافة الشقق للعمارة</li>
                                <li>تحديد أسعار كل شقة</li>
                                <li>إدارة الحجوزات والدفعات</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> تنبيه</h6>
                            <ul class="mb-0">
                                <li>تأكد من صحة البيانات قبل الحفظ</li>
                                <li>لا يمكن حذف العمارة إذا كانت تحتوي على حجوزات</li>
                                <li>يمكن تعديل البيانات لاحقاً</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // التحقق من صحة النموذج
    $('form').on('submit', function(e) {
        var name = $('#name').val().trim();
        var location = $('#location').val().trim();
        var floors = parseInt($('#total_floors').val());
        
        if (!name || !location || !floors || floors < 1) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
            return false;
        }
        
        if (floors > 20) {
            e.preventDefault();
            alert('عدد الطوابق لا يمكن أن يتجاوز 20 طابق');
            return false;
        }
    });
    
    // تنسيق الحقول
    $('#name').on('input', function() {
        this.value = this.value.replace(/[^a-zA-Zأ-ي\s0-9]/g, '');
    });
});
</script>
{% endblock %}
