{% extends "base.html" %}

{% block title %}إضافة عمارة جديدة - نظام الاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-building"></i>
                        إضافة عمارة جديدة
                    </h3>
                </div>
                
                <form method="POST">
                    {{ form.hidden_tag() }}
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.name.label(class="form-label") }}
                                    <i class="fas fa-building"></i>
                                    <span class="text-danger">*</span>
                                    {{ form.name(class="form-control", placeholder="مثال: عمارة الأسرة الجامعية") }}
                                    {% if form.name.errors %}
                                        <div class="text-danger">
                                            {% for error in form.name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.total_floors.label(class="form-label") }}
                                    <i class="fas fa-layer-group"></i>
                                    <span class="text-danger">*</span>
                                    {{ form.total_floors(class="form-control", placeholder="مثال: 5") }}
                                    {% if form.total_floors.errors %}
                                        <div class="text-danger">
                                            {% for error in form.total_floors.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.location.label(class="form-label") }}
                            <i class="fas fa-map-marker-alt"></i>
                            <span class="text-danger">*</span>
                            {{ form.location(class="form-control", placeholder="مثال: سيدي فرج - الجزائر العاصمة") }}
                            {% if form.location.errors %}
                                <div class="text-danger">
                                    {% for error in form.location.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            <i class="fas fa-info-circle"></i>
                            {{ form.description(class="form-control", rows="4", placeholder="وصف مختصر عن العمارة ومرافقها...") }}
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.vacation_buildings_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                العودة للقائمة
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> نصائح</h6>
                            <ul class="mb-0">
                                <li>اختر اسماً واضحاً ومميزاً للعمارة</li>
                                <li>حدد الموقع بدقة لسهولة الوصول</li>
                                <li>أضف وصفاً شاملاً للمرافق المتاحة</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> بعد الإضافة</h6>
                            <ul class="mb-0">
                                <li>يمكنك إضافة الشقق للعمارة</li>
                                <li>تحديد أسعار كل شقة</li>
                                <li>إدارة الحجوزات والدفعات</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> تنبيه</h6>
                            <ul class="mb-0">
                                <li>تأكد من صحة البيانات قبل الحفظ</li>
                                <li>لا يمكن حذف العمارة إذا كانت تحتوي على حجوزات</li>
                                <li>يمكن تعديل البيانات لاحقاً</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // التحقق من صحة النموذج
    $('form').on('submit', function(e) {
        var name = $('#name').val().trim();
        var location = $('#location').val().trim();
        var floors = parseInt($('#total_floors').val());
        
        if (!name || !location || !floors || floors < 1) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
            return false;
        }
        
        if (floors > 20) {
            e.preventDefault();
            alert('عدد الطوابق لا يمكن أن يتجاوز 20 طابق');
            return false;
        }
    });
    
    // تنسيق الحقول
    $('#name').on('input', function() {
        this.value = this.value.replace(/[^a-zA-Zأ-ي\s0-9]/g, '');
    });
});
</script>
{% endblock %}
