{% extends "base.html" %}

{% block title %}إضافة عامل لقائمة الاحتياط{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus text-warning"></i>
                        إضافة عامل لقائمة الاحتياط
                    </h3>
                </div>

                <form method="POST">
                    {{ form.hidden_tag() }}
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.worker_id.label(class="form-label") }}
                                    {{ form.worker_id(class="form-control select2") }}
                                    {% if form.worker_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.worker_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.apartment_id.label(class="form-label") }}
                                    {{ form.apartment_id(class="form-control select2") }}
                                    {% if form.apartment_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.apartment_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.preferred_start_date.label(class="form-label") }}
                                    {{ form.preferred_start_date(class="form-control", type="date") }}
                                    {% if form.preferred_start_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.preferred_start_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.preferred_end_date.label(class="form-label") }}
                                    {{ form.preferred_end_date(class="form-control", type="date") }}
                                    {% if form.preferred_end_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.preferred_end_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            {{ form.priority.label(class="form-label") }}
                            {{ form.priority(class="form-control", min="1", max="10") }}
                            <small class="form-text text-muted">الأولوية من 1 (الأعلى) إلى 10 (الأقل)</small>
                            {% if form.priority.errors %}
                                <div class="text-danger">
                                    {% for error in form.priority.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- معلومات مهمة -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> معلومات مهمة:</h5>
                            <ul class="mb-0">
                                <li>سيتم ترتيب قائمة الاحتياط حسب الأولوية ثم تاريخ التسجيل</li>
                                <li>عند إلغاء حجز، سيتم إشعار العمال في قائمة الاحتياط تلقائياً</li>
                                <li>الأولوية 1 هي الأعلى، والأولوية 10 هي الأقل</li>
                                <li>يمكن للعامل الواحد أن يكون في قائمة الاحتياط لعدة شقق</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.submit(class="btn btn-primary btn-block") }}
                            </div>
                            <div class="col-md-6">
                                <a href="{{ url_for('main.vacation_waiting_list') }}" class="btn btn-secondary btn-block">
                                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // تفعيل Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        dir: 'rtl'
    });

    // تعيين تاريخ افتراضي (الأسبوع القادم)
    var nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    var nextWeekStr = nextWeek.toISOString().split('T')[0];
    
    var weekAfter = new Date();
    weekAfter.setDate(weekAfter.getDate() + 14);
    var weekAfterStr = weekAfter.toISOString().split('T')[0];
    
    $('#preferred_start_date').val(nextWeekStr);
    $('#preferred_end_date').val(weekAfterStr);

    // التحقق من صحة التواريخ
    $('#preferred_start_date, #preferred_end_date').change(function() {
        var startDate = new Date($('#preferred_start_date').val());
        var endDate = new Date($('#preferred_end_date').val());
        
        if (endDate <= startDate) {
            alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            $('#preferred_end_date').focus();
        }
    });
});
</script>
{% endblock %}
