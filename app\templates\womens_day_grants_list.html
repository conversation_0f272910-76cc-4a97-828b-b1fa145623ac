{% extends "base.html" %}

{% block title %}
منح 8 مارس - عيد المرأة - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-gift text-pink me-2"></i>
                    منح 8 مارس - عيد المرأة
                </h5>
                <a href="{{ url_for('main.add_womens_day_grant') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة منحة جديدة
                </a>
            </div>
            <div class="card-body">
                <!-- إحصائيات سريعة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-female fa-2x mb-2"></i>
                                <h4>{{ total_female_workers }}</h4>
                                <p class="mb-0">عدد العاملات الإناث</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-gift fa-2x mb-2"></i>
                                <h4>{{ grants|length }}</h4>
                                <p class="mb-0">عدد المنح المسجلة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <h4>{{ grants[0].year if grants else 'لا يوجد' }}</h4>
                                <p class="mb-0">آخر منحة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill fa-2x mb-2"></i>
                                <h4>{{ grants[0].formatted_total_amount if grants else '0.00 دج' }}</h4>
                                <p class="mb-0">آخر مبلغ إجمالي</p>
                            </div>
                        </div>
                    </div>
                </div>

                {% if grants %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>السنة</th>
                                <th>مبلغ المنحة الفردية</th>
                                <th>عدد المستفيدات</th>
                                <th>المبلغ الإجمالي</th>
                                <th>تاريخ الصرف</th>
                                <th>حالة التوقيعات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for grant in grants %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary fs-6">{{ grant.year }}</span>
                                </td>
                                <td>
                                    <strong class="text-success">{{ grant.formatted_grant_amount }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ grant.total_female_workers }} عاملة</span>
                                </td>
                                <td>
                                    <strong class="text-danger">{{ grant.formatted_total_amount }}</strong>
                                </td>
                                <td>{{ grant.issue_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% set signed_count = grant.recipients|selectattr('signature_status', 'equalto', true)|list|length %}
                                    {% set total_count = grant.recipients|length %}
                                    {% if signed_count == total_count %}
                                        <span class="badge bg-success">مكتملة ({{ signed_count }}/{{ total_count }})</span>
                                    {% elif signed_count > 0 %}
                                        <span class="badge bg-warning">جزئية ({{ signed_count }}/{{ total_count }})</span>
                                    {% else %}
                                        <span class="badge bg-danger">لم تبدأ (0/{{ total_count }})</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('main.womens_day_grant_detail', id=grant.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('main.edit_womens_day_grant', id=grant.id) }}" 
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('main.print_womens_day_grant', id=grant.id) }}" 
                                           class="btn btn-sm btn-outline-success" title="طباعة" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete({{ grant.id }}, '{{ grant.year }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد منح مسجلة</h4>
                    <p class="text-muted">ابدأ بإضافة منحة 8 مارس الأولى</p>
                    <a href="{{ url_for('main.add_womens_day_grant') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة منحة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نموذج تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف منحة 8 مارس لسنة <strong id="deleteYear"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم حذف جميع بيانات المستفيدات المرتبطة بهذه المنحة!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(grantId, year) {
    document.getElementById('deleteYear').textContent = year;
    document.getElementById('deleteForm').action = `/delete_womens_day_grant/${grantId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>

<style>
.text-pink {
    color: #e91e63 !important;
}

.card-body .card {
    transition: transform 0.2s;
}

.card-body .card:hover {
    transform: translateY(-2px);
}

.table th {
    border-top: none;
}

.btn-group .btn {
    margin: 0 1px;
}
</style>
{% endblock %}
