#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت بناء الملف التنفيذي
"""

import os
import shutil
import subprocess
import sys

def clean_build_dirs():
    """تنظيف مجلدات البناء السابقة"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ تم حذف مجلد: {dir_name}")

def create_spec_file():
    """إنشاء ملف .spec مخصص"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('app/templates', 'app/templates'),
        ('app/static', 'app/static'),
        ('instance', 'instance'),
    ],
    hiddenimports=[
        'flask',
        'flask_sqlalchemy',
        'flask_wtf',
        'wtforms',
        'PIL',
        'sqlite3',
        'datetime',
        'os',
        'sys',
        'webbrowser',
        'threading',
        'time'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_لجنة_الخدمات_الاجتماعية',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app/static/images/logo.png' if os.path.exists('app/static/images/logo.png') else None,
)
'''
    
    with open('sosiel.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✅ تم إنشاء ملف sosiel.spec")

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 جاري بناء الملف التنفيذي...")
    
    try:
        # تشغيل PyInstaller
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            'sosiel.spec'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في بناء الملف التنفيذي:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء الملف التنفيذي: {str(e)}")
        return False

def create_readme():
    """إنشاء ملف تعليمات"""
    readme_content = """# نظام لجنة الخدمات الاجتماعية للعمال

## كيفية التشغيل:
1. انقر نقراً مزدوجاً على ملف "نظام_لجنة_الخدمات_الاجتماعية.exe"
2. انتظر حتى يتم تحميل التطبيق (قد يستغرق بضع ثوانٍ في المرة الأولى)
3. سيتم فتح التطبيق في المتصفح تلقائياً
4. إذا لم يفتح المتصفح تلقائياً، اذهب إلى: http://127.0.0.1:5000

## ملاحظات مهمة:
- لا تغلق نافذة الأوامر (Command Prompt) أثناء استخدام التطبيق
- لإيقاف التطبيق، اضغط Ctrl+C في نافذة الأوامر أو أغلقها
- يتم حفظ البيانات في مجلد "instance" بجانب الملف التنفيذي
- تأكد من وجود اتصال بالإنترنت لتحميل الخطوط والأيقونات

## المتطلبات:
- نظام تشغيل Windows 7 أو أحدث
- لا حاجة لتثبيت Python أو أي برامج إضافية

## الدعم الفني:
في حالة وجود مشاكل، تأكد من:
1. تشغيل الملف كمسؤول (Run as Administrator)
2. إيقاف برامج مكافحة الفيروسات مؤقتاً
3. التأكد من عدم استخدام المنفذ 5000 من برامج أخرى
"""
    
    with open('dist/README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ تم إنشاء ملف التعليمات")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏗️  بناء الملف التنفيذي لنظام لجنة الخدمات الاجتماعية")
    print("=" * 60)
    
    # تنظيف المجلدات السابقة
    clean_build_dirs()
    
    # إنشاء ملف .spec
    create_spec_file()
    
    # بناء الملف التنفيذي
    if build_executable():
        # إنشاء ملف التعليمات
        create_readme()
        
        print("=" * 60)
        print("🎉 تم بناء الملف التنفيذي بنجاح!")
        print(f"📁 المجلد: {os.path.abspath('dist')}")
        print("📄 الملف: نظام_لجنة_الخدمات_الاجتماعية.exe")
        print("=" * 60)
        print("💡 يمكنك الآن نسخ مجلد 'dist' إلى أي جهاز آخر وتشغيل التطبيق")
    else:
        print("❌ فشل في بناء الملف التنفيذي")

if __name__ == '__main__':
    main()
