{% extends "base.html" %}

{% block title %}
إحصائيات الأطفال - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>إحصائيات الأطفال
                </h5>
                <a href="/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>العودة للرئيسية
                </a>
            </div>
            <div class="card-body">
                <!-- إجمالي الأطفال -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <h4><i class="fas fa-child me-2"></i>إجمالي الأطفال: {{ total_children }}</h4>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات حسب المستوى التعليمي -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-graduation-cap me-2"></i>التوزيع حسب المستوى التعليمي
                                </h6>
                            </div>
                            <div class="card-body">
                                {% if education_stats %}
                                    <div class="row">
                                        {% for stat in education_stats %}
                                        <div class="col-md-3 mb-3">
                                            <div class="card text-center border-primary">
                                                <div class="card-body">
                                                    {% if stat.education_level == 'ابتدائي' %}
                                                        <i class="fas fa-child fa-2x text-primary mb-2"></i>
                                                    {% elif stat.education_level == 'متوسط' %}
                                                        <i class="fas fa-user-graduate fa-2x text-success mb-2"></i>
                                                    {% elif stat.education_level == 'ثانوي' %}
                                                        <i class="fas fa-book fa-2x text-warning mb-2"></i>
                                                    {% elif stat.education_level == 'جامعي' %}
                                                        <i class="fas fa-university fa-2x text-danger mb-2"></i>
                                                    {% endif %}
                                                    <h5 class="card-title">{{ stat.education_level }}</h5>
                                                    <h3 class="text-primary">{{ stat.count }}</h3>
                                                    <small class="text-muted">
                                                        {% if total_children > 0 %}
                                                            {{ "%.1f"|format((stat.count / total_children) * 100) }}%
                                                        {% else %}
                                                            0%
                                                        {% endif %}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-center py-4">
                                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">لا توجد بيانات أطفال</h5>
                                        <p class="text-muted">لم يتم تسجيل أي أطفال بعد</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات حسب الجنس -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-venus-mars me-2"></i>التوزيع حسب الجنس
                                </h6>
                            </div>
                            <div class="card-body">
                                {% if gender_stats %}
                                    <div class="row">
                                        {% for stat in gender_stats %}
                                        <div class="col-6 text-center">
                                            <div class="border rounded p-3 mb-2">
                                                {% if stat.gender == 'ذكر' %}
                                                    <i class="fas fa-mars fa-2x text-primary mb-2"></i>
                                                    <h5 class="text-primary">ذكور</h5>
                                                {% else %}
                                                    <i class="fas fa-venus fa-2x text-danger mb-2"></i>
                                                    <h5 class="text-danger">إناث</h5>
                                                {% endif %}
                                                <h3>{{ stat.count }}</h3>
                                                <small class="text-muted">
                                                    {% if total_children > 0 %}
                                                        {{ "%.1f"|format((stat.count / total_children) * 100) }}%
                                                    {% else %}
                                                        0%
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="text-center py-3">
                                        <p class="text-muted">لا توجد بيانات</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات حسب العمر -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-birthday-cake me-2"></i>التوزيع حسب الفئة العمرية
                                </h6>
                            </div>
                            <div class="card-body">
                                {% if age_ranges %}
                                    {% for age_range, count in age_ranges.items() %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>{{ age_range }}</span>
                                        <div>
                                            <span class="badge bg-primary">{{ count }}</span>
                                            <small class="text-muted ms-1">
                                                {% if total_children > 0 %}
                                                    ({{ "%.1f"|format((count / total_children) * 100) }}%)
                                                {% else %}
                                                    (0%)
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="text-center py-3">
                                        <p class="text-muted">لا توجد بيانات</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- رسم بياني بسيط -->
                {% if education_stats %}
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>الرسم البياني للمستويات التعليمية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for stat in education_stats %}
                                    <div class="col-md-3 mb-3">
                                        <div class="text-center">
                                            <div class="progress mb-2" style="height: 20px;">
                                                {% set percentage = (stat.count / total_children) * 100 if total_children > 0 else 0 %}
                                                <div class="progress-bar 
                                                    {% if stat.education_level == 'ابتدائي' %}bg-primary
                                                    {% elif stat.education_level == 'متوسط' %}bg-success
                                                    {% elif stat.education_level == 'ثانوي' %}bg-warning
                                                    {% elif stat.education_level == 'جامعي' %}bg-danger
                                                    {% endif %}" 
                                                    role="progressbar" 
                                                    style="width: {{ percentage }}%"
                                                    aria-valuenow="{{ percentage }}" 
                                                    aria-valuemin="0" 
                                                    aria-valuemax="100">
                                                    {{ "%.0f"|format(percentage) }}%
                                                </div>
                                            </div>
                                            <small class="text-muted">{{ stat.education_level }}</small>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
