{% extends "base.html" %}

{% block title %}قائمة دفعات الاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-money-bill-wave text-success"></i>
                        قائمة دفعات الاصطياف
                    </h3>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-money-bill-wave"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي الدفعات</span>
                                    <span class="info-box-number">{{ total_payments }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">دفعات مدفوعة</span>
                                    <span class="info-box-number">{{ paid_payments }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">دفعات معلقة</span>
                                    <span class="info-box-number">{{ pending_payments }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-exclamation-triangle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">دفعات متأخرة</span>
                                    <span class="info-box-number">{{ overdue_payments }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلترة -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="booking_number" class="form-control" 
                                       placeholder="رقم الحجز" value="{{ booking_number }}">
                            </div>
                            <div class="col-md-4">
                                <select name="status" class="form-control">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>في الانتظار</option>
                                    <option value="paid" {% if status == 'paid' %}selected{% endif %}>مدفوع</option>
                                    <option value="overdue" {% if status == 'overdue' %}selected{% endif %}>متأخر</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ url_for('main.vacation_payments_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> مسح الفلتر
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>رقم الدفعة</th>
                                    <th>رقم الحجز</th>
                                    <th>العامل</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>تاريخ الدفع</th>
                                    <th>طريقة الدفع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments.items %}
                                <tr>
                                    <td>
                                        <small class="text-primary">{{ payment.payment_number }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('main.vacation_booking_detail', id=payment.booking.id) }}" class="text-decoration-none">
                                            {{ payment.booking.booking_number }}
                                        </a>
                                    </td>
                                    <td>{{ payment.booking.worker.full_name }}</td>
                                    <td><strong>{{ payment.formatted_amount }}</strong></td>
                                    <td>{{ payment.due_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if payment.payment_date %}
                                            {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.payment_method %}
                                            {% if payment.payment_method == 'cash' %}
                                                <span class="badge badge-success">نقدي</span>
                                            {% elif payment.payment_method == 'transfer' %}
                                                <span class="badge badge-info">تحويل بنكي</span>
                                            {% elif payment.payment_method == 'check' %}
                                                <span class="badge badge-warning">شيك</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.status == 'paid' %}
                                            <span class="badge badge-success">{{ payment.status_display }}</span>
                                        {% elif payment.status == 'pending' %}
                                            <span class="badge badge-warning">{{ payment.status_display }}</span>
                                        {% elif payment.status == 'overdue' %}
                                            <span class="badge badge-danger">{{ payment.status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.status == 'pending' %}
                                        <button class="btn btn-sm btn-success" onclick="markAsPaid({{ payment.id }})">
                                            <i class="fas fa-check"></i> دفع
                                        </button>
                                        {% endif %}
                                        <a href="{{ url_for('main.vacation_booking_detail', id=payment.booking.id) }}" 
                                           class="btn btn-sm btn-info" title="عرض الحجز">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد دفعات
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    {% if payments.pages > 1 %}
                    <nav aria-label="تصفح الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if payments.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_payments_list', page=payments.prev_num, booking_number=booking_number, status=status) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in payments.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != payments.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.vacation_payments_list', page=page_num, booking_number=booking_number, status=status) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if payments.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_payments_list', page=payments.next_num, booking_number=booking_number, status=status) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function markAsPaid(paymentId) {
    if (confirm('هل تم دفع هذه الدفعة؟')) {
        fetch(`/vacation_payment/${paymentId}/mark_paid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}
</script>
{% endblock %}
