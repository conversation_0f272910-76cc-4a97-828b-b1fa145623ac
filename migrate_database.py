#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات لإضافة العنوان الشخصي والهاتف
"""

import sqlite3
import os

def migrate_database():
    """تحديث قاعدة البيانات"""
    db_path = 'instance/workers_committee.db'
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # التحقق من وجود العمود job_title
        cursor.execute("PRAGMA table_info(worker)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'job_title' in columns:
            print("بدء تحديث قاعدة البيانات...")
            
            # إضافة الأعمدة الجديدة
            try:
                cursor.execute("ALTER TABLE worker ADD COLUMN personal_address TEXT")
                print("تم إضافة عمود العنوان الشخصي")
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e):
                    print(f"خطأ في إضافة عمود العنوان: {e}")
            
            try:
                cursor.execute("ALTER TABLE worker ADD COLUMN phone TEXT")
                print("تم إضافة عمود الهاتف")
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e):
                    print(f"خطأ في إضافة عمود الهاتف: {e}")
            
            # نقل البيانات من job_title إلى personal_address
            cursor.execute("UPDATE worker SET personal_address = job_title WHERE personal_address IS NULL")
            cursor.execute("UPDATE worker SET phone = '0000000000' WHERE phone IS NULL")
            
            print("تم نقل البيانات من الوظيفة إلى العنوان الشخصي")
            print("تم تعيين رقم هاتف افتراضي للعمال الموجودين")
            
            # حفظ التغييرات
            conn.commit()
            print("تم حفظ التغييرات بنجاح!")
            
        else:
            print("قاعدة البيانات محدثة بالفعل أو لا تحتوي على بيانات قديمة")
            
    except Exception as e:
        print(f"خطأ في تحديث قاعدة البيانات: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
