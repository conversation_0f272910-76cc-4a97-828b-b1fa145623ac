{% extends "base.html" %}

{% block title %}إضافة مخيم جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus text-success"></i>
                        إضافة مخيم جديد
                    </h3>
                </div>

                <form method="POST">
                    {{ form.hidden_tag() }}
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control") }}
                                    {% if form.name.errors %}
                                        <div class="text-danger">
                                            {% for error in form.name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.location.label(class="form-label") }}
                                    {{ form.location(class="form-control") }}
                                    {% if form.location.errors %}
                                        <div class="text-danger">
                                            {% for error in form.location.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="3") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.duration_days.label(class="form-label") }}
                                    {{ form.duration_days(class="form-control") }}
                                    <small class="form-text text-muted">المدة الموحدة لجميع الإقامات</small>
                                    {% if form.duration_days.errors %}
                                        <div class="text-danger">
                                            {% for error in form.duration_days.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.start_date.label(class="form-label") }}
                                    {{ form.start_date(class="form-control", type="date") }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.start_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    {{ form.end_date.label(class="form-label") }}
                                    {{ form.end_date(class="form-control", type="date") }}
                                    {% if form.end_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.end_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                        </div>

                        <!-- معلومات مهمة -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> معلومات مهمة:</h5>
                            <ul class="mb-0">
                                <li>بعد إنشاء المخيم، يمكنك إضافة العمارات والشقق</li>
                                <li>المدة الموحدة ستطبق على جميع القرعات في هذا المخيم</li>
                                <li>يمكن تعديل تواريخ الموسم لاحقاً</li>
                                <li>المخيمات غير النشطة لن تظهر في القرعات الجديدة</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.submit(class="btn btn-success btn-block") }}
                            </div>
                            <div class="col-md-6">
                                <a href="{{ url_for('main.vacation_camps_list') }}" class="btn btn-secondary btn-block">
                                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // تعيين تواريخ افتراضية
    var today = new Date();
    var nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    var endOfYear = new Date(today.getFullYear(), 11, 31);
    
    if (!$('#start_date').val()) {
        $('#start_date').val(nextMonth.toISOString().split('T')[0]);
    }
    
    if (!$('#end_date').val()) {
        $('#end_date').val(endOfYear.toISOString().split('T')[0]);
    }
});
</script>
{% endblock %}
