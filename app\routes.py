from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from werkzeug.utils import secure_filename
from sqlalchemy import func
from app.models import db, Worker, Child, Bank, ThermalBathReceipt, WomensDayGrant, WomensDayGrantRecipient
from app.forms import WorkerForm, BankForm, ThermalBathReceiptForm, WomensDayGrantForm, WomensDayGrantUpdateForm
import os
from datetime import datetime
import pandas as pd
from io import BytesIO
try:
    from PIL import Image
except ImportError:
    Image = None

main = Blueprint('main', __name__)

def allowed_file(filename):
    """التحقق من نوع الملف المسموح"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_registration_number():
    """توليد رقم تسجيل جديد للعامل"""
    # الحصول على أعلى رقم تسجيل موجود
    last_worker = Worker.query.filter(
        Worker.registration_number.like('EMP%')
    ).order_by(Worker.registration_number.desc()).first()

    if last_worker and last_worker.registration_number:
        try:
            # استخراج الرقم من آخر رقم تسجيل
            last_number = int(last_worker.registration_number[3:])  # إزالة "EMP"
            new_number = last_number + 1
        except (ValueError, IndexError):
            new_number = 1
    else:
        new_number = 1

    return f"EMP{new_number:04d}"

def save_picture(form_picture):
    """حفظ الصورة وإرجاع اسم الملف"""
    if form_picture and allowed_file(form_picture.filename):
        # إنشاء اسم ملف فريد
        filename = secure_filename(form_picture.filename)
        name, ext = os.path.splitext(filename)
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{name}{ext}"

        # مسار حفظ الملف
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)

        # حفظ الصورة
        try:
            if Image:
                # تصغير الصورة وحفظها
                image = Image.open(form_picture)
                # تصغير الصورة إلى 300x300 بكسل كحد أقصى
                image.thumbnail((300, 300), Image.Resampling.LANCZOS)
                image.save(file_path, optimize=True, quality=85)
            else:
                # حفظ الصورة بدون تصغير إذا لم تكن PIL متوفرة
                form_picture.save(file_path)
            return filename
        except Exception as e:
            flash(f'خطأ في حفظ الصورة: {str(e)}', 'error')
            return None
    return None

@main.route('/')
def index():
    """الصفحة الرئيسية"""
    # إحصائيات
    total_workers = Worker.query.count()
    total_children = Child.query.count()
    female_workers = Worker.query.filter_by(gender='أنثى').count()

    # آخر العمال المضافين
    recent_workers = Worker.query.order_by(Worker.created_at.desc()).limit(5).all()

    return render_template('index.html',
                         total_workers=total_workers,
                         total_children=total_children,
                         female_workers=female_workers,
                         recent_workers=recent_workers)

@main.route('/children_stats')
def children_stats():
    """إحصائيات الأطفال حسب المستوى التعليمي"""
    from sqlalchemy import func

    # إحصائيات حسب المستوى التعليمي
    education_stats = db.session.query(
        Child.education_level,
        func.count(Child.id).label('count')
    ).group_by(Child.education_level).all()

    # إحصائيات حسب الجنس
    gender_stats = db.session.query(
        Child.gender,
        func.count(Child.id).label('count')
    ).group_by(Child.gender).all()

    # إحصائيات حسب العمر (تقريبية)
    from datetime import date
    current_year = date.today().year

    age_groups = []
    children = Child.query.all()

    age_ranges = {
        '6-10 سنوات': 0,
        '11-15 سنة': 0,
        '16-20 سنة': 0,
        '21+ سنة': 0
    }

    for child in children:
        age = current_year - child.birth_date.year
        if 6 <= age <= 10:
            age_ranges['6-10 سنوات'] += 1
        elif 11 <= age <= 15:
            age_ranges['11-15 سنة'] += 1
        elif 16 <= age <= 20:
            age_ranges['16-20 سنة'] += 1
        else:
            age_ranges['21+ سنة'] += 1

    total_children = Child.query.count()

    return render_template('children_stats.html',
                         education_stats=education_stats,
                         gender_stats=gender_stats,
                         age_ranges=age_ranges,
                         total_children=total_children)

@main.route('/female_workers')
def female_workers():
    """قائمة النساء العاملات فقط"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)

    # استعلام النساء فقط
    query = Worker.query.filter_by(gender='أنثى')

    # البحث إذا تم تحديده
    if search:
        query = query.filter(
            db.or_(
                Worker.first_name.contains(search),
                Worker.last_name.contains(search),
                Worker.position.contains(search),
                Worker.workplace.contains(search)
            )
        )

    # ترقيم الصفحات
    workers = query.paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('female_workers.html',
                         workers=workers,
                         search=search,
                         total_female_workers=Worker.query.filter_by(gender='أنثى').count())

@main.route('/workers')
def workers_list():
    """قائمة العمال"""
    from .forms import WorkerForm
    form = WorkerForm()  # إنشاء نموذج للحصول على CSRF token

    search = request.args.get('search', '')

    if search:
        workers = Worker.query.filter(
            # البيانات الشخصية
            (Worker.first_name.contains(search)) |
            (Worker.last_name.contains(search)) |
            (Worker.birth_place.contains(search)) |
            (Worker.personal_address.contains(search)) |
            (Worker.phone.contains(search)) |
            (Worker.gender.contains(search)) |
            (Worker.marital_status.contains(search)) |
            # البحث في تاريخ الميلاد كنص (مثال: 1985, 1985-01, 1985-01-01)
            (func.strftime('%Y-%m-%d', Worker.birth_date).contains(search)) |
            (func.strftime('%Y', Worker.birth_date).contains(search)) |

            # بيانات العمل
            (Worker.position.contains(search)) |
            (Worker.workplace.contains(search)) |

            # معلومات الهوية والضمان
            (Worker.registration_number.contains(search)) |
            (Worker.social_security_number.contains(search)) |
            (Worker.national_id_number.contains(search)) |
            (Worker.id_type.contains(search)) |
            (Worker.id_issue_place.contains(search)) |
            # البحث في تاريخ إصدار الهوية
            (func.strftime('%Y-%m-%d', Worker.id_issue_date).contains(search)) |
            (func.strftime('%Y', Worker.id_issue_date).contains(search)) |

            # معلومات الحساب البنكي
            (Worker.account_type.contains(search)) |
            (Worker.account_number.contains(search)) |
            (Worker.bank_name.contains(search)) |
            (Worker.bank_agency.contains(search))
        ).order_by(Worker.created_at.desc()).all()
    else:
        workers = Worker.query.order_by(Worker.created_at.desc()).all()

    return render_template('workers_list.html', workers=workers, form=form)

@main.route('/worker/<int:id>')
def worker_detail(id):
    """تفاصيل العامل"""
    worker = Worker.query.get_or_404(id)
    return render_template('worker_detail.html', worker=worker)

@main.route('/add_worker', methods=['GET', 'POST'])
def add_worker():
    """إضافة عامل جديد"""
    form = WorkerForm()

    if request.method == 'POST':
        try:
            # توليد رقم تسجيل جديد
            registration_number = generate_registration_number()

            # إنشاء عامل جديد
            worker = Worker(
                registration_number=registration_number,
                first_name=request.form.get('first_name'),
                last_name=request.form.get('last_name'),
                birth_date=datetime.strptime(request.form.get('birth_date'), '%Y-%m-%d').date(),
                birth_place=request.form.get('birth_place'),
                personal_address=request.form.get('personal_address'),
                phone=request.form.get('phone'),
                position=request.form.get('position'),
                workplace=request.form.get('workplace'),
                gender=request.form.get('gender'),
                marital_status=request.form.get('marital_status'),

                # معلومات الهوية والضمان الاجتماعي
                social_security_number=request.form.get('social_security_number') if request.form.get('social_security_number') else None,
                id_type=request.form.get('id_type') if request.form.get('id_type') else None,
                national_id_number=request.form.get('national_id_number') if request.form.get('national_id_number') else None,
                id_issue_date=datetime.strptime(request.form.get('id_issue_date'), '%Y-%m-%d').date() if request.form.get('id_issue_date') else None,
                id_issue_place=request.form.get('id_issue_place') if request.form.get('id_issue_place') else None,

                # معلومات الحساب البنكي/البريدي
                account_type=request.form.get('account_type') if request.form.get('account_type') else None,
                account_number=request.form.get('account_number') if request.form.get('account_number') else None,
                bank_name=request.form.get('bank_name') if request.form.get('bank_name') else None,
                bank_agency=request.form.get('bank_agency') if request.form.get('bank_agency') else None
            )

            # حفظ الصورة إن وجدت
            if 'photo' in request.files and request.files['photo'].filename:
                photo = request.files['photo']
                filename = save_picture(photo)
                if filename:
                    worker.photo_filename = filename

            # حفظ العامل في قاعدة البيانات
            db.session.add(worker)
            db.session.flush()  # للحصول على ID العامل

            # إضافة الأطفال
            child_index = 0
            while f'children-{child_index}-name' in request.form:
                child_name = request.form.get(f'children-{child_index}-name')
                if child_name:  # التأكد من وجود اسم الطفل
                    child = Child(
                        worker_id=worker.id,
                        name=child_name,
                        birth_date=datetime.strptime(request.form.get(f'children-{child_index}-birth_date'), '%Y-%m-%d').date(),
                        gender=request.form.get(f'children-{child_index}-gender'),
                        education_level=request.form.get(f'children-{child_index}-education_level')
                    )
                    db.session.add(child)
                child_index += 1

            db.session.commit()
            flash(f'تم إضافة العامل {worker.full_name} برقم التسجيل {worker.registration_number} بنجاح!', 'success')
            return redirect(url_for('main.worker_detail', id=worker.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة العامل: {str(e)}', 'error')

    return render_template('worker_form.html', form=form, worker=None)

@main.route('/edit_worker/<int:id>', methods=['GET', 'POST'])
def edit_worker(id):
    """تعديل بيانات العامل"""
    worker = Worker.query.get_or_404(id)
    form = WorkerForm()

    if form.validate_on_submit():
        try:
            # تحديث بيانات العامل الأساسية
            worker.first_name = form.first_name.data
            worker.last_name = form.last_name.data
            worker.birth_date = form.birth_date.data
            worker.birth_place = form.birth_place.data
            worker.personal_address = form.personal_address.data
            worker.phone = form.phone.data
            worker.position = form.position.data
            worker.workplace = form.workplace.data
            worker.gender = form.gender.data
            worker.marital_status = form.marital_status.data

            # تحديث معلومات الهوية والضمان الاجتماعي
            worker.social_security_number = form.social_security_number.data if form.social_security_number.data else None
            worker.id_type = form.id_type.data if form.id_type.data else None
            worker.national_id_number = form.national_id_number.data if form.national_id_number.data else None
            worker.id_issue_date = form.id_issue_date.data if form.id_issue_date.data else None
            worker.id_issue_place = form.id_issue_place.data if form.id_issue_place.data else None

            # تحديث معلومات الحساب البنكي/البريدي
            worker.account_type = form.account_type.data if form.account_type.data else None
            worker.account_number = form.account_number.data if form.account_number.data else None
            worker.bank_name = form.bank_name.data if form.bank_name.data else None
            worker.bank_agency = form.bank_agency.data if form.bank_agency.data else None

            worker.updated_at = datetime.now()

            # تحديث الصورة إن وجدت
            photo = form.photo.data
            if photo and photo.filename:
                if allowed_file(photo.filename):
                    # حذف الصورة القديمة
                    if worker.photo_filename:
                        old_file = os.path.join(current_app.config['UPLOAD_FOLDER'], worker.photo_filename)
                        if os.path.exists(old_file):
                            os.remove(old_file)

                    # حفظ الصورة الجديدة
                    filename = save_picture(photo)
                    if filename:
                        worker.photo_filename = filename

            # معالجة بيانات الأطفال من الطلب مباشرة
            Child.query.filter_by(worker_id=worker.id).delete()

            # الحصول على بيانات الأطفال من الطلب
            i = 0
            while f'children-{i}-name' in request.form:
                name = request.form.get(f'children-{i}-name', '').strip()
                if name:  # إذا كان هناك اسم
                    birth_date_str = request.form.get(f'children-{i}-birth_date')
                    gender = request.form.get(f'children-{i}-gender')
                    education_level = request.form.get(f'children-{i}-education_level')

                    if birth_date_str:
                        try:
                            birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
                            child = Child(
                                worker_id=worker.id,
                                name=name,
                                birth_date=birth_date,
                                gender=gender,
                                education_level=education_level
                            )
                            db.session.add(child)
                        except ValueError:
                            pass  # تجاهل التواريخ غير الصحيحة
                i += 1

            db.session.commit()
            flash(f'تم تحديث بيانات العامل {worker.full_name} بنجاح!', 'success')
            return redirect(url_for('main.worker_detail', id=worker.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث البيانات: {str(e)}', 'error')
            print(f"خطأ في تحديث العامل: {e}")
            import traceback
            traceback.print_exc()

    # ملء النموذج ببيانات العامل الحالية
    if request.method == 'GET':
        # رقم التسجيل
        form.registration_number.data = worker.registration_number

        # البيانات الأساسية
        form.first_name.data = worker.first_name
        form.last_name.data = worker.last_name
        form.birth_date.data = worker.birth_date
        form.birth_place.data = worker.birth_place
        form.personal_address.data = worker.personal_address
        form.phone.data = worker.phone
        form.position.data = worker.position
        form.workplace.data = worker.workplace
        form.gender.data = worker.gender
        form.marital_status.data = worker.marital_status

        # معلومات الهوية والضمان الاجتماعي
        form.social_security_number.data = worker.social_security_number
        form.id_type.data = worker.id_type
        form.national_id_number.data = worker.national_id_number
        form.id_issue_date.data = worker.id_issue_date
        form.id_issue_place.data = worker.id_issue_place

        # ملء بيانات الحساب البنكي/البريدي
        form.account_type.data = worker.account_type
        form.account_number.data = worker.account_number
        form.bank_name.data = worker.bank_name
        form.bank_agency.data = worker.bank_agency

    return render_template('edit_worker.html', form=form, worker=worker)

@main.route('/delete_worker/<int:id>', methods=['POST'])
def delete_worker(id):
    """حذف العامل"""
    worker = Worker.query.get_or_404(id)
    
    # حذف صورة العامل إن وجدت
    if worker.photo_filename:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], worker.photo_filename)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    # حذف العامل (سيتم حذف الأطفال تلقائياً بسبب cascade)
    worker_name = worker.full_name
    db.session.delete(worker)
    db.session.commit()
    
    flash(f'تم حذف العامل {worker_name} بنجاح!', 'success')
    return redirect(url_for('main.workers_list'))

@main.route('/import_workers', methods=['GET', 'POST'])
def import_workers():
    """استيراد العمال من ملف Excel"""
    if request.method == 'POST':
        if 'excel_file' not in request.files:
            flash('لم يتم اختيار ملف!', 'error')
            return redirect(request.url)

        file = request.files['excel_file']
        if file.filename == '':
            flash('لم يتم اختيار ملف!', 'error')
            return redirect(request.url)

        if file and allowed_excel_file(file.filename):
            try:
                # قراءة ملف Excel
                df = pd.read_excel(file, engine='openpyxl')

                # التحقق من وجود الأعمدة المطلوبة
                required_columns = ['الاسم', 'اللقب', 'تاريخ_الميلاد', 'مكان_الميلاد',
                                  'الوظيفة', 'المنصب', 'مكان_العمل', 'الجنس', 'الحالة_الاجتماعية']

                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    flash(f'الأعمدة التالية مفقودة في الملف: {", ".join(missing_columns)}', 'error')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # التحقق من البيانات المطلوبة
                        if pd.isna(row['الاسم']) or pd.isna(row['اللقب']):
                            errors.append(f'الصف {index + 2}: الاسم واللقب مطلوبان')
                            error_count += 1
                            continue

                        # تحويل تاريخ الميلاد
                        birth_date = None
                        if not pd.isna(row['تاريخ_الميلاد']):
                            if isinstance(row['تاريخ_الميلاد'], str):
                                try:
                                    birth_date = datetime.strptime(row['تاريخ_الميلاد'], '%Y-%m-%d').date()
                                except ValueError:
                                    try:
                                        birth_date = datetime.strptime(row['تاريخ_الميلاد'], '%d/%m/%Y').date()
                                    except ValueError:
                                        errors.append(f'الصف {index + 2}: تاريخ الميلاد غير صحيح')
                                        error_count += 1
                                        continue
                            else:
                                birth_date = row['تاريخ_الميلاد'].date() if hasattr(row['تاريخ_الميلاد'], 'date') else row['تاريخ_الميلاد']

                        if not birth_date:
                            errors.append(f'الصف {index + 2}: تاريخ الميلاد مطلوب')
                            error_count += 1
                            continue

                        # دالة مساعدة لتحويل القيم بأمان
                        def safe_str(value):
                            if pd.isna(value):
                                return ''
                            return str(value).strip()

                        # إنشاء العامل
                        worker = Worker(
                            first_name=safe_str(row['الاسم']),
                            last_name=safe_str(row['اللقب']),
                            birth_date=birth_date,
                            birth_place=safe_str(row['مكان_الميلاد']),
                            personal_address=safe_str(row['العنوان_الشخصي']),
                            phone=safe_str(row['الهاتف']),
                            position=safe_str(row['المنصب']),
                            workplace=safe_str(row['مكان_العمل']),
                            gender=safe_str(row['الجنس']) or 'ذكر',
                            marital_status=safe_str(row['الحالة_الاجتماعية']) or 'عازب'
                        )

                        db.session.add(worker)
                        success_count += 1

                    except Exception as e:
                        errors.append(f'الصف {index + 2}: خطأ في المعالجة - {str(e)}')
                        error_count += 1
                        continue

                # حفظ البيانات
                if success_count > 0:
                    db.session.commit()
                    flash(f'تم استيراد {success_count} عامل بنجاح!', 'success')
                else:
                    db.session.rollback()

                if error_count > 0:
                    flash(f'فشل في استيراد {error_count} صف. الأخطاء: {"; ".join(errors[:5])}', 'warning')

                return redirect(url_for('main.workers_list'))

            except Exception as e:
                db.session.rollback()
                flash(f'خطأ في قراءة الملف: {str(e)}', 'error')
                return redirect(request.url)
        else:
            flash('نوع الملف غير مدعوم! يرجى استخدام ملفات Excel (.xlsx, .xls)', 'error')

    return render_template('import_workers.html')

def allowed_excel_file(filename):
    """التحقق من نوع ملف Excel"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ['xlsx', 'xls']

@main.route('/download_template')
def download_template():
    """تحميل نموذج Excel للعمال"""
    from flask import send_file, make_response

    # إنشاء DataFrame مع البيانات النموذجية
    data = {
        'الاسم': ['أحمد', 'فاطمة', 'محمد'],
        'اللقب': ['بن علي', 'بن محمد', 'قاسمي'],
        'تاريخ_الميلاد': ['1985-03-15', '1990-07-22', '1988-12-10'],
        'مكان_الميلاد': ['الجلفة', 'حاسي بحبح', 'عين الإبل'],
        'الوظيفة': ['مهندس', 'معلمة', 'طبيب'],
        'المنصب': ['مهندس أول', 'معلمة رئيسية', 'طبيب عام'],
        'مكان_العمل': ['مديرية التربية', 'ابتدائية النور', 'مستشفى الجلفة'],
        'الجنس': ['ذكر', 'أنثى', 'ذكر'],
        'الحالة_الاجتماعية': ['متزوج', 'متزوج', 'عازب']
    }

    df = pd.DataFrame(data)

    # إنشاء ملف Excel في الذاكرة
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='العمال', index=False)

        # تنسيق الجدول
        workbook = writer.book
        worksheet = writer.sheets['العمال']

        # تعديل عرض الأعمدة
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    # إرسال الملف
    response = make_response(output.read())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response.headers['Content-Disposition'] = 'attachment; filename=نموذج_العمال.xlsx'

    return response

@main.route('/logo_settings', methods=['GET', 'POST'])
def logo_settings():
    """إعدادات الشعار والخلفية المائية"""
    if request.method == 'POST':
        if 'logo' in request.files and request.files['logo'].filename:
            logo_file = request.files['logo']
            if logo_file and allowed_file(logo_file.filename):
                # حذف الشعار القديم إن وجد
                old_logo_path = os.path.join(current_app.config['UPLOAD_FOLDER'], '..', 'images', 'logo.png')
                if os.path.exists(old_logo_path):
                    os.remove(old_logo_path)

                # حفظ الشعار الجديد
                logo_path = os.path.join(current_app.config['UPLOAD_FOLDER'], '..', 'images', 'logo.png')

                try:
                    if Image:
                        # تصغير الشعار وحفظه
                        image = Image.open(logo_file)
                        # تحويل إلى RGBA للشفافية
                        if image.mode != 'RGBA':
                            image = image.convert('RGBA')
                        # تصغير الشعار إلى 500x500 بكسل كحد أقصى
                        image.thumbnail((500, 500), Image.Resampling.LANCZOS)
                        image.save(logo_path, 'PNG', optimize=True)
                    else:
                        # حفظ الشعار بدون تصغير إذا لم تكن PIL متوفرة
                        logo_file.save(logo_path)

                    flash('تم تحديث الشعار بنجاح!', 'success')
                except Exception as e:
                    flash(f'خطأ في حفظ الشعار: {str(e)}', 'error')
            else:
                flash('نوع الملف غير مدعوم! يرجى استخدام صور PNG, JPG, أو GIF', 'error')

        return redirect(url_for('main.logo_settings'))

    # التحقق من وجود الشعار
    logo_path = os.path.join(current_app.config['UPLOAD_FOLDER'], '..', 'images', 'logo.png')
    logo_exists = os.path.exists(logo_path)

    return render_template('logo_settings.html', logo_exists=logo_exists)

@main.route('/banks')
def banks_list():
    """قائمة البنوك"""
    banks = Bank.query.order_by(Bank.name).all()

    # حساب عدد العمال لكل بنك
    bank_worker_counts = {}
    for bank in banks:
        worker_count = Worker.query.filter_by(bank_name=bank.name).count()
        bank_worker_counts[bank.id] = worker_count

    return render_template('banks_list.html', banks=banks, bank_worker_counts=bank_worker_counts)

@main.route('/add_bank', methods=['GET', 'POST'])
def add_bank():
    """إضافة بنك جديد"""
    form = BankForm()

    if form.validate_on_submit():
        try:
            # التحقق من عدم وجود البنك مسبقاً
            existing_bank = Bank.query.filter_by(name=form.bank_name.data.strip()).first()
            if existing_bank:
                flash('هذا البنك موجود مسبقاً!', 'warning')
                return render_template('add_bank.html', form=form)

            # إنشاء بنك جديد
            bank = Bank(name=form.bank_name.data.strip())
            db.session.add(bank)
            db.session.commit()

            flash(f'تم إضافة البنك "{bank.name}" بنجاح!', 'success')
            return redirect(url_for('main.banks_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة البنك: {str(e)}', 'error')

    return render_template('add_bank.html', form=form)

@main.route('/delete_bank/<int:id>', methods=['POST'])
def delete_bank(id):
    """حذف بنك"""
    bank = Bank.query.get_or_404(id)

    try:
        # التحقق من عدم استخدام البنك في أي عامل
        workers_using_bank = Worker.query.filter_by(bank_name=bank.name).count()
        if workers_using_bank > 0:
            flash(f'لا يمكن حذف البنك "{bank.name}" لأنه مستخدم من قبل {workers_using_bank} عامل/عاملة', 'warning')
        else:
            db.session.delete(bank)
            db.session.commit()
            flash(f'تم حذف البنك "{bank.name}" بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف البنك: {str(e)}', 'error')

    return redirect(url_for('main.banks_list'))

@main.route('/toggle_bank/<int:id>', methods=['POST'])
def toggle_bank(id):
    """تفعيل/إلغاء تفعيل بنك"""
    bank = Bank.query.get_or_404(id)

    try:
        bank.is_active = not bank.is_active
        db.session.commit()

        status = "تم تفعيل" if bank.is_active else "تم إلغاء تفعيل"
        flash(f'{status} البنك "{bank.name}" بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث حالة البنك: {str(e)}', 'error')

    return redirect(url_for('main.banks_list'))

# ==================== وصولات الحمام المعدني ====================

@main.route('/thermal_receipts')
def thermal_receipts_list():
    """قائمة وصولات الحمام المعدني"""
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # البحث
    search = request.args.get('search', '')
    query = ThermalBathReceipt.query.join(Worker)

    if search:
        query = query.filter(
            db.or_(
                Worker.first_name.contains(search),
                Worker.last_name.contains(search),
                ThermalBathReceipt.receipt_number.contains(search),
                ThermalBathReceipt.treatment_location.contains(search)
            )
        )

    receipts = query.order_by(ThermalBathReceipt.issue_date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('thermal_receipts_list.html', receipts=receipts, search=search)

@main.route('/add_thermal_receipt', methods=['GET', 'POST'])
def add_thermal_receipt():
    """إضافة وصل حمام معدني جديد"""
    form = ThermalBathReceiptForm()

    # إذا تم تمرير معرف العامل في الرابط، قم بتحديده مسبقاً
    worker_id = request.args.get('worker_id', type=int)
    if worker_id and request.method == 'GET':
        form.worker_id.data = worker_id

    if form.validate_on_submit():
        try:
            # التحقق من وجود العامل
            worker = Worker.query.get(form.worker_id.data)
            if not worker:
                flash('العامل المحدد غير موجود!', 'error')
                return render_template('add_thermal_receipt.html', form=form)

            # حساب دفع العامل
            worker_payment = form.total_amount.data - form.committee_contribution.data

            # توليد رقم الوصل
            from datetime import datetime
            current_year = datetime.now().year
            last_receipt = ThermalBathReceipt.query.filter(
                ThermalBathReceipt.receipt_number.like(f'{current_year}%')
            ).order_by(ThermalBathReceipt.receipt_number.desc()).first()

            if last_receipt:
                last_number = int(last_receipt.receipt_number.split('/')[-1])
                new_number = last_number + 1
            else:
                new_number = 1

            receipt_number = f'{current_year}/{new_number:04d}'

            # إنشاء الوصل
            receipt = ThermalBathReceipt(
                receipt_number=receipt_number,
                worker_id=form.worker_id.data,
                issue_date=form.issue_date.data,
                total_amount=form.total_amount.data,
                committee_contribution=form.committee_contribution.data,
                worker_payment=worker_payment,
                treatment_location=form.treatment_location.data,
                notes=form.notes.data
            )

            db.session.add(receipt)
            db.session.commit()

            flash(f'تم إصدار الوصل رقم {receipt_number} للعامل {worker.full_name} بنجاح!', 'success')
            return redirect(url_for('main.thermal_receipt_detail', id=receipt.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إصدار الوصل: {str(e)}', 'error')

    return render_template('add_thermal_receipt.html', form=form)

@main.route('/thermal_receipt/<int:id>')
def thermal_receipt_detail(id):
    """تفاصيل وصل الحمام المعدني"""
    receipt = ThermalBathReceipt.query.get_or_404(id)
    return render_template('thermal_receipt_detail.html', receipt=receipt)

@main.route('/print_thermal_receipt/<int:id>')
def print_thermal_receipt(id):
    """طباعة وصل الحمام المعدني"""
    receipt = ThermalBathReceipt.query.get_or_404(id)

    # التحقق من وجود الشعار
    logo_path = os.path.join(current_app.config['UPLOAD_FOLDER'], '..', 'images', 'logo.png')
    logo_exists = os.path.exists(logo_path)

    return render_template('print_thermal_receipt.html', receipt=receipt, logo_exists=logo_exists)

@main.route('/edit_thermal_receipt/<int:id>', methods=['GET', 'POST'])
def edit_thermal_receipt(id):
    """تعديل وصل الحمام المعدني"""
    receipt = ThermalBathReceipt.query.get_or_404(id)
    form = ThermalBathReceiptForm()

    if form.validate_on_submit():
        try:
            # تحديث بيانات الوصل
            receipt.worker_id = form.worker_id.data
            receipt.issue_date = form.issue_date.data
            receipt.total_amount = form.total_amount.data
            receipt.committee_contribution = form.committee_contribution.data
            receipt.worker_payment = form.total_amount.data - form.committee_contribution.data
            receipt.treatment_location = form.treatment_location.data
            receipt.notes = form.notes.data

            db.session.commit()
            flash('تم تحديث الوصل بنجاح!', 'success')
            return redirect(url_for('main.thermal_receipt_detail', id=receipt.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الوصل: {str(e)}', 'error')

    # ملء النموذج ببيانات الوصل الحالية
    if request.method == 'GET':
        form.worker_id.data = receipt.worker_id
        form.issue_date.data = receipt.issue_date
        form.total_amount.data = receipt.total_amount
        form.committee_contribution.data = receipt.committee_contribution
        form.treatment_location.data = receipt.treatment_location
        form.notes.data = receipt.notes

    return render_template('edit_thermal_receipt.html', form=form, receipt=receipt)

@main.route('/delete_thermal_receipt/<int:id>', methods=['POST'])
def delete_thermal_receipt(id):
    """حذف وصل الحمام المعدني"""
    receipt = ThermalBathReceipt.query.get_or_404(id)

    try:
        receipt_number = receipt.receipt_number
        db.session.delete(receipt)
        db.session.commit()
        flash(f'تم حذف الوصل رقم {receipt_number} بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الوصل: {str(e)}', 'error')

    return redirect(url_for('main.thermal_receipts_list'))


# ==================== منحة 8 مارس ====================

@main.route('/womens_day_grants')
def womens_day_grants_list():
    """قائمة منح 8 مارس"""
    grants = WomensDayGrant.query.order_by(WomensDayGrant.year.desc()).all()

    # إحصائيات
    total_female_workers = Worker.query.filter_by(gender='أنثى').count()

    return render_template('womens_day_grants_list.html',
                         grants=grants,
                         total_female_workers=total_female_workers)

@main.route('/womens_day_grant/<int:id>')
def womens_day_grant_detail(id):
    """تفاصيل منحة 8 مارس"""
    grant = WomensDayGrant.query.get_or_404(id)

    # قائمة المستفيدات
    recipients = WomensDayGrantRecipient.query.filter_by(grant_id=id).join(Worker).order_by(Worker.last_name, Worker.first_name).all()

    return render_template('womens_day_grant_detail.html',
                         grant=grant,
                         recipients=recipients)

@main.route('/add_womens_day_grant', methods=['GET', 'POST'])
def add_womens_day_grant():
    """إضافة منحة 8 مارس جديدة"""
    form = WomensDayGrantForm()

    if form.validate_on_submit():
        try:
            # حساب عدد العاملات الإناث
            female_workers = Worker.query.filter_by(gender='أنثى').all()
            total_female_workers = len(female_workers)

            if total_female_workers == 0:
                flash('لا توجد عاملات إناث في النظام!', 'warning')
                return redirect(request.url)

            # حساب المبلغ الإجمالي
            total_amount = form.grant_amount.data * total_female_workers

            # إنشاء المنحة
            grant = WomensDayGrant(
                year=form.year.data,
                grant_amount=form.grant_amount.data,
                total_female_workers=total_female_workers,
                total_amount=total_amount,
                issue_date=form.issue_date.data,
                notes=form.notes.data
            )

            db.session.add(grant)
            db.session.flush()  # للحصول على ID المنحة

            # إضافة جميع العاملات الإناث كمستفيدات
            for worker in female_workers:
                recipient = WomensDayGrantRecipient(
                    grant_id=grant.id,
                    worker_id=worker.id,
                    amount_received=form.grant_amount.data,
                    received_date=form.issue_date.data,
                    signature_status=False
                )
                db.session.add(recipient)

            db.session.commit()

            flash(f'تم إنشاء منحة 8 مارس لسنة {grant.year} بنجاح! '
                  f'عدد المستفيدات: {total_female_workers} عاملة، '
                  f'المبلغ الإجمالي: {grant.formatted_total_amount}', 'success')

            return redirect(url_for('main.womens_day_grant_detail', id=grant.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء المنحة: {str(e)}', 'error')

    # إحصائيات للعرض
    total_female_workers = Worker.query.filter_by(gender='أنثى').count()

    return render_template('add_womens_day_grant.html',
                         form=form,
                         total_female_workers=total_female_workers)

@main.route('/edit_womens_day_grant/<int:id>', methods=['GET', 'POST'])
def edit_womens_day_grant(id):
    """تعديل منحة 8 مارس"""
    grant = WomensDayGrant.query.get_or_404(id)
    form = WomensDayGrantUpdateForm()

    if form.validate_on_submit():
        try:
            # تحديث بيانات المنحة
            old_amount = grant.grant_amount
            grant.grant_amount = form.grant_amount.data
            grant.issue_date = form.issue_date.data
            grant.notes = form.notes.data
            grant.updated_at = datetime.utcnow()

            # إعادة حساب المبلغ الإجمالي
            grant.total_amount = grant.grant_amount * grant.total_female_workers

            # تحديث مبالغ المستفيدات إذا تغير المبلغ
            if old_amount != grant.grant_amount:
                recipients = WomensDayGrantRecipient.query.filter_by(grant_id=grant.id).all()
                for recipient in recipients:
                    recipient.amount_received = grant.grant_amount
                    recipient.received_date = grant.issue_date

            db.session.commit()

            flash(f'تم تحديث منحة 8 مارس لسنة {grant.year} بنجاح!', 'success')
            return redirect(url_for('main.womens_day_grant_detail', id=grant.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث المنحة: {str(e)}', 'error')

    # ملء النموذج بالبيانات الحالية
    if request.method == 'GET':
        form.grant_amount.data = grant.grant_amount
        form.issue_date.data = grant.issue_date
        form.notes.data = grant.notes

    return render_template('edit_womens_day_grant.html',
                         form=form,
                         grant=grant)

@main.route('/delete_womens_day_grant/<int:id>', methods=['POST'])
def delete_womens_day_grant(id):
    """حذف منحة 8 مارس"""
    grant = WomensDayGrant.query.get_or_404(id)

    try:
        grant_year = grant.year
        db.session.delete(grant)  # سيحذف المستفيدات تلقائياً بسبب CASCADE
        db.session.commit()

        flash(f'تم حذف منحة 8 مارس لسنة {grant_year} بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المنحة: {str(e)}', 'error')

    return redirect(url_for('main.womens_day_grants_list'))

@main.route('/womens_day_grant/<int:grant_id>/toggle_signature/<int:recipient_id>')
def toggle_signature_status(grant_id, recipient_id):
    """تبديل حالة التوقيع للمستفيدة"""
    recipient = WomensDayGrantRecipient.query.get_or_404(recipient_id)

    try:
        recipient.signature_status = not recipient.signature_status
        db.session.commit()

        status_text = "تم التوقيع" if recipient.signature_status else "لم يتم التوقيع"
        flash(f'تم تحديث حالة التوقيع للعاملة {recipient.worker.full_name}: {status_text}', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تحديث حالة التوقيع: {str(e)}', 'error')

    return redirect(url_for('main.womens_day_grant_detail', id=grant_id))

@main.route('/womens_day_grant/<int:id>/print')
def print_womens_day_grant(id):
    """طباعة منحة 8 مارس"""
    grant = WomensDayGrant.query.get_or_404(id)
    recipients = WomensDayGrantRecipient.query.filter_by(grant_id=id).join(Worker).order_by(Worker.last_name, Worker.first_name).all()

    return render_template('print_womens_day_grant.html',
                         grant=grant,
                         recipients=recipients)
