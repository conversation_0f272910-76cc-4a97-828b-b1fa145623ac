{% extends "base.html" %}

{% block title %}قائمة حجوزات الاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-check text-primary"></i>
                        قائمة حجوزات الاصطياف
                    </h3>
                    <a href="{{ url_for('main.add_vacation_booking') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة حجز جديد
                    </a>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي الحجوزات</span>
                                    <span class="info-box-number">{{ total_bookings }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">حجوزات مؤكدة</span>
                                    <span class="info-box-number">{{ confirmed_bookings }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">حجوزات مكتملة</span>
                                    <span class="info-box-number">{{ completed_bookings }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-times-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">حجوزات ملغية</span>
                                    <span class="info-box-number">{{ cancelled_bookings }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلترة -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="worker_name" class="form-control" 
                                       placeholder="اسم العامل" value="{{ worker_name }}">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">جميع الحالات</option>
                                    <option value="confirmed" {% if status == 'confirmed' %}selected{% endif %}>مؤكد</option>
                                    <option value="completed" {% if status == 'completed' %}selected{% endif %}>مكتمل</option>
                                    <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="apartment_id" class="form-control">
                                    <option value="">جميع الشقق</option>
                                    {% for apartment in apartments %}
                                    <option value="{{ apartment.id }}" {% if apartment_id == apartment.id %}selected{% endif %}>
                                        {{ apartment.full_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ url_for('main.vacation_bookings_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> مسح الفلتر
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>رقم الحجز</th>
                                    <th>العامل</th>
                                    <th>الشقة</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>المدة</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in bookings.items %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ booking.booking_number }}</strong>
                                    </td>
                                    <td>{{ booking.worker.full_name }}</td>
                                    <td>{{ booking.apartment.full_name }}</td>
                                    <td>{{ booking.start_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ booking.end_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ booking.duration_days }} يوم</td>
                                    <td>{{ booking.formatted_total_amount }}</td>
                                    <td>
                                        {% if booking.status == 'confirmed' %}
                                            <span class="badge badge-success">{{ booking.status_display }}</span>
                                        {% elif booking.status == 'completed' %}
                                            <span class="badge badge-info">{{ booking.status_display }}</span>
                                        {% elif booking.status == 'cancelled' %}
                                            <span class="badge badge-danger">{{ booking.status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('main.vacation_booking_detail', id=booking.id) }}" 
                                               class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if booking.status != 'cancelled' %}
                                            <a href="{{ url_for('main.edit_vacation_booking', id=booking.id) }}" 
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد حجوزات
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    {% if bookings.pages > 1 %}
                    <nav aria-label="تصفح الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if bookings.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_bookings_list', page=bookings.prev_num, worker_name=worker_name, status=status, apartment_id=apartment_id) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in bookings.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != bookings.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.vacation_bookings_list', page=page_num, worker_name=worker_name, status=status, apartment_id=apartment_id) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if bookings.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_bookings_list', page=bookings.next_num, worker_name=worker_name, status=status, apartment_id=apartment_id) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
