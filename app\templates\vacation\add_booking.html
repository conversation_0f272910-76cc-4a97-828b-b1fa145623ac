{% extends "base.html" %}

{% block title %}إضافة حجز اصطياف جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus text-primary"></i>
                        إضافة حجز اصطياف جديد
                    </h3>
                </div>

                <form method="POST">
                    {{ form.hidden_tag() }}
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.worker_id.label(class="form-label") }}
                                    {{ form.worker_id(class="form-control select2") }}
                                    {% if form.worker_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.worker_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.apartment_id.label(class="form-label") }}
                                    {{ form.apartment_id(class="form-control select2") }}
                                    {% if form.apartment_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.apartment_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.start_date.label(class="form-label") }}
                                    {{ form.start_date(class="form-control", type="date") }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.start_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.end_date.label(class="form-label") }}
                                    {{ form.end_date(class="form-control", type="date") }}
                                    {% if form.end_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.end_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- معاينة الحساب -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-calculator"></i> معاينة الحساب:</h5>
                            <div id="calculation-preview">
                                <p><strong>المدة:</strong> <span id="duration">-</span> يوم</p>
                                <p><strong>عدد الأسابيع:</strong> <span id="weeks">-</span></p>
                                <p><strong>سعر الأسبوع:</strong> <span id="weekly-price">-</span></p>
                                <p><strong>المبلغ الإجمالي:</strong> <span id="total-amount">-</span></p>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.submit(class="btn btn-primary btn-block") }}
                            </div>
                            <div class="col-md-6">
                                <a href="{{ url_for('main.vacation_bookings_list') }}" class="btn btn-secondary btn-block">
                                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // تفعيل Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        dir: 'rtl'
    });

    // حساب التكلفة عند تغيير التواريخ أو الشقة
    function calculateCost() {
        var startDate = $('#start_date').val();
        var endDate = $('#end_date').val();
        var apartmentId = $('#apartment_id').val();

        if (startDate && endDate && apartmentId) {
            var start = new Date(startDate);
            var end = new Date(endDate);
            var duration = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

            if (duration > 0) {
                // الحصول على سعر الشقة (يجب تمرير البيانات من الخادم)
                var apartmentPrice = getApartmentPrice(apartmentId);
                var weeks = duration / 7;
                var totalAmount = weeks * apartmentPrice;

                $('#duration').text(duration);
                $('#weeks').text(weeks.toFixed(2));
                $('#weekly-price').text(apartmentPrice.toLocaleString() + ' دج');
                $('#total-amount').text(totalAmount.toLocaleString() + ' دج');
            }
        }
    }

    function getApartmentPrice(apartmentId) {
        // هذه دالة مؤقتة - يجب الحصول على السعر من الخادم
        return 15000; // سعر افتراضي
    }

    $('#start_date, #end_date, #apartment_id').change(calculateCost);
});
</script>
{% endblock %}
