{% extends "base.html" %}

{% block title %}قائمة المخيمات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-campground text-success"></i>
                        قائمة المخيمات
                    </h3>
                    <div>
                        <a href="{{ url_for('main.add_vacation_camp') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة مخيم جديد
                        </a>
                        <a href="{{ url_for('main.check_expired_occupations') }}" class="btn btn-warning">
                            <i class="fas fa-sync"></i> فحص الإقامات المنتهية
                        </a>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-primary">
                                <span class="info-box-icon"><i class="fas fa-campground"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي المخيمات</span>
                                    <span class="info-box-number">{{ total_camps }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">مخيمات نشطة</span>
                                    <span class="info-box-number">{{ active_camps }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-building"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي العمارات</span>
                                    <span class="info-box-number">{{ total_buildings }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-home"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي الشقق</span>
                                    <span class="info-box-number">{{ total_apartments }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلترة -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="camp_name" class="form-control" 
                                       placeholder="اسم المخيم" value="{{ camp_name }}">
                            </div>
                            <div class="col-md-3">
                                <input type="text" name="location" class="form-control" 
                                       placeholder="الموقع" value="{{ location }}">
                            </div>
                            <div class="col-md-3">
                                <select name="is_active" class="form-control">
                                    <option value="">جميع الحالات</option>
                                    <option value="1" {% if is_active == '1' %}selected{% endif %}>نشط</option>
                                    <option value="0" {% if is_active == '0' %}selected{% endif %}>غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-info btn-block">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>اسم المخيم</th>
                                    <th>الموقع</th>
                                    <th>مدة الإقامة</th>
                                    <th>العمارات</th>
                                    <th>الشقق</th>
                                    <th>الإشغال</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for camp in camps.items %}
                                <tr>
                                    <td>
                                        <strong>{{ camp.name }}</strong>
                                        {% if camp.description %}
                                        <br><small class="text-muted">{{ camp.description[:50] }}...</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ camp.location }}</td>
                                    <td>
                                        <span class="badge badge-info">{{ camp.duration_days }} يوم</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ camp.buildings.count() }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ camp.total_apartments }}</span>
                                    </td>
                                    <td>
                                        {% set occupancy_rate = (camp.occupied_apartments / camp.total_apartments * 100) if camp.total_apartments > 0 else 0 %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar 
                                                {% if occupancy_rate < 50 %}bg-success
                                                {% elif occupancy_rate < 80 %}bg-warning
                                                {% else %}bg-danger{% endif %}" 
                                                role="progressbar" style="width: {{ occupancy_rate }}%">
                                                {{ "%.1f"|format(occupancy_rate) }}%
                                            </div>
                                        </div>
                                        <small>{{ camp.occupied_apartments }}/{{ camp.total_apartments }}</small>
                                    </td>
                                    <td>
                                        {% if camp.is_active %}
                                            <span class="badge badge-success">نشط</span>
                                        {% else %}
                                            <span class="badge badge-secondary">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('main.vacation_camp_detail', id=camp.id) }}" 
                                               class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('main.edit_vacation_camp', id=camp.id) }}" 
                                               class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد مخيمات
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    {% if camps.pages > 1 %}
                    <nav aria-label="تصفح الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if camps.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_camps_list', page=camps.prev_num, camp_name=camp_name, location=location, is_active=is_active) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in camps.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != camps.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.vacation_camps_list', page=page_num, camp_name=camp_name, location=location, is_active=is_active) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if camps.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_camps_list', page=camps.next_num, camp_name=camp_name, location=location, is_active=is_active) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
