# 🏖️ دليل نظام الاصطياف - لجنة الخدمات الاجتماعية

## 🚀 كيفية التشغيل

### الطريقة الأولى (الأسهل):
1. **انقر مرتين** على ملف `start_vacation_system.bat`
2. **انتظر** حتى يظهر رابط التطبيق
3. **افتح المتصفح** واذهب إلى `http://localhost:5000`

### الطريقة الثانية:
1. افتح **Command Prompt** في مجلد المشروع
2. اكتب: `python start_server.py`
3. افتح المتصفح واذهب إلى `http://localhost:5000`

---

## 🏗️ نظام الاصطياف - الميزات الجديدة

### ✅ ما تم إنجازه:

#### 🏢 **إدارة العمارات:**
- إضافة عمارات جديدة
- تحديد الموقع وعدد الطوابق
- عرض إحصائيات العمارات

#### 🏠 **إدارة الشقق:**
- إضافة شقق لكل عمارة
- تحديد الطابق ورقم الشقة
- تحديد السعة والسعر الأسبوعي
- فلترة الشقق حسب العمارة والحالة

#### 📊 **الإحصائيات:**
- عدد العمارات الإجمالي
- عدد الشقق المتاحة
- إجمالي السعة
- الحجوزات النشطة

---

## 🎯 المراحل القادمة (قيد التطوير):

### 📅 **نظام الحجوزات:**
- ربط العمال بالشقق
- تحديد فترات الإقامة
- حساب التكلفة الإجمالية

### 💰 **نظام الدفعات:**
- تقسيط المبالغ (كل 7 أيام)
- تتبع حالة الدفعات
- تسجيل المدفوعات

### 📋 **قائمة الاحتياط:**
- تسجيل العمال المنتظرين
- إشعار تلقائي عند الإلغاء
- نظام الأولوية

---

## 🔧 استكشاف الأخطاء

### ❌ **التطبيق لا يعمل:**
1. تأكد من تثبيت Python
2. شغل: `pip install -r requirements.txt`
3. جرب: `python start_server.py`

### ❌ **خطأ في قاعدة البيانات:**
1. احذف ملف `instance/workers_committee.db`
2. شغل التطبيق مرة أخرى

### ❌ **المنفذ مشغول:**
1. أغلق أي تطبيق آخر على المنفذ 5000
2. أو غير المنفذ في `start_server.py`

---

## 📱 كيفية الاستخدام

### 1️⃣ **إضافة عمارة:**
- اذهب إلى "نظام الاصطياف" → "إدارة العمارات"
- اضغط "إضافة عمارة جديدة"
- املأ البيانات: الاسم، الموقع، عدد الطوابق

### 2️⃣ **إضافة شقق:**
- اذهب إلى "نظام الاصطياف" → "إدارة الشقق"
- اضغط "إضافة شقة جديدة"
- اختر العمارة وحدد تفاصيل الشقة

### 3️⃣ **عرض الإحصائيات:**
- الإحصائيات تظهر تلقائياً في أسفل كل صفحة
- تتضمن: عدد العمارات، الشقق، السعة الإجمالية

---

## 🎨 الواجهة

### 🎯 **القائمة الرئيسية:**
- قائمة منسدلة "نظام الاصطياف" في الشريط العلوي
- روابط سريعة لجميع الأقسام

### 📊 **الجداول:**
- فلترة متقدمة للبيانات
- أزرار إجراءات سريعة
- عرض معلومات مفصلة

### 🎨 **التصميم:**
- واجهة عربية متجاوبة
- ألوان متناسقة
- أيقونات واضحة

---

## 📞 الدعم الفني

إذا واجهت أي مشكلة:
1. تحقق من هذا الدليل أولاً
2. جرب إعادة تشغيل التطبيق
3. تأكد من تثبيت جميع المتطلبات

---

## 🎉 ملاحظات مهمة

- ✅ النظام يحفظ البيانات تلقائياً
- ✅ يمكن تعديل البيانات لاحقاً
- ✅ النسخ الاحتياطية تتم تلقائياً
- ⚠️ لا تحذف ملفات قاعدة البيانات يدوياً

**🚀 استمتع بنظام الاصطياف الجديد! 🏖️**
