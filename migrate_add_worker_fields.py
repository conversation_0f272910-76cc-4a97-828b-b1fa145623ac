#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ترحيل قاعدة البيانات - إضافة حقول جديدة للعامل
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    db_path = 'instance/workers_committee.db'
    if os.path.exists(db_path):
        backup_path = f'instance/workers_committee_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return True
    return False

def add_new_fields():
    """إضافة الحقول الجديدة لجدول العمال"""
    db_path = 'instance/workers_committee.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # التحقق من الحقول الموجودة
        cursor.execute("PRAGMA table_info(workers)")
        existing_columns = [column[1] for column in cursor.fetchall()]
        
        # قائمة الحقول الجديدة المطلوب إضافتها
        new_fields = [
            ('registration_number', 'VARCHAR(20) UNIQUE', 'رقم التسجيل الخاص'),
            ('social_security_number', 'VARCHAR(20)', 'رقم الضمان الاجتماعي'),
            ('id_type', 'VARCHAR(50)', 'نوع الهوية'),
            ('national_id_number', 'VARCHAR(20)', 'رقم بطاقة التعريف الوطنية'),
            ('id_issue_date', 'DATE', 'تاريخ صدور الهوية'),
            ('id_issue_place', 'VARCHAR(200)', 'مكان صدور الهوية')
        ]
        
        # إضافة الحقول الجديدة
        for field_name, field_type, description in new_fields:
            if field_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE workers ADD COLUMN {field_name} {field_type}")
                    print(f"✅ تم إضافة حقل: {field_name} ({description})")
                except sqlite3.Error as e:
                    print(f"⚠️ خطأ في إضافة حقل {field_name}: {e}")
            else:
                print(f"ℹ️ الحقل {field_name} موجود مسبقاً")
        
        # إنشاء فهرس لرقم التسجيل
        try:
            cursor.execute("CREATE UNIQUE INDEX IF NOT EXISTS idx_registration_number ON workers(registration_number)")
            print("✅ تم إنشاء فهرس رقم التسجيل")
        except sqlite3.Error as e:
            print(f"⚠️ خطأ في إنشاء الفهرس: {e}")
        
        # إنشاء فهرس لرقم الضمان الاجتماعي
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_social_security ON workers(social_security_number)")
            print("✅ تم إنشاء فهرس رقم الضمان الاجتماعي")
        except sqlite3.Error as e:
            print(f"⚠️ خطأ في إنشاء فهرس الضمان: {e}")
        
        # إنشاء فهرس لرقم الهوية الوطنية
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_national_id ON workers(national_id_number)")
            print("✅ تم إنشاء فهرس رقم الهوية الوطنية")
        except sqlite3.Error as e:
            print(f"⚠️ خطأ في إنشاء فهرس الهوية: {e}")
        
        conn.commit()
        print("✅ تم حفظ التغييرات بنجاح!")
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ خطأ في ترحيل قاعدة البيانات: {e}")
        return False
    finally:
        conn.close()

def generate_registration_numbers():
    """توليد أرقام تسجيل للعمال الموجودين"""
    db_path = 'instance/workers_committee.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # الحصول على العمال الذين لا يملكون رقم تسجيل
        cursor.execute("SELECT id, first_name, last_name FROM workers WHERE registration_number IS NULL OR registration_number = ''")
        workers_without_reg = cursor.fetchall()
        
        if not workers_without_reg:
            print("ℹ️ جميع العمال لديهم أرقام تسجيل")
            return
        
        print(f"🔢 توليد أرقام تسجيل لـ {len(workers_without_reg)} عامل...")
        
        # الحصول على أعلى رقم تسجيل موجود
        cursor.execute("SELECT MAX(CAST(SUBSTR(registration_number, 4) AS INTEGER)) FROM workers WHERE registration_number LIKE 'EMP%'")
        max_num = cursor.fetchone()[0] or 0
        
        # توليد أرقام تسجيل جديدة
        for worker_id, first_name, last_name in workers_without_reg:
            max_num += 1
            reg_number = f"EMP{max_num:04d}"  # مثال: EMP0001, EMP0002
            
            cursor.execute("UPDATE workers SET registration_number = ? WHERE id = ?", (reg_number, worker_id))
            print(f"✅ {last_name} {first_name}: {reg_number}")
        
        conn.commit()
        print("✅ تم توليد أرقام التسجيل بنجاح!")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ خطأ في توليد أرقام التسجيل: {e}")
    finally:
        conn.close()

def verify_migration():
    """التحقق من نجاح الترحيل"""
    db_path = 'instance/workers_committee.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # عرض هيكل الجدول الجديد
        cursor.execute("PRAGMA table_info(workers)")
        columns = cursor.fetchall()
        
        print("\n📋 هيكل جدول العمال بعد الترحيل:")
        print("-" * 60)
        for column in columns:
            print(f"العمود: {column[1]}, النوع: {column[2]}, مطلوب: {'نعم' if column[3] else 'لا'}")
        
        # عرض عدد العمال
        cursor.execute("SELECT COUNT(*) FROM workers")
        count = cursor.fetchone()[0]
        print(f"\nعدد العمال: {count}")
        
        # عرض عينة من أرقام التسجيل
        cursor.execute("SELECT registration_number, first_name, last_name FROM workers WHERE registration_number IS NOT NULL LIMIT 5")
        sample_workers = cursor.fetchall()
        
        if sample_workers:
            print("\n📝 عينة من أرقام التسجيل:")
            print("-" * 40)
            for reg_num, first_name, last_name in sample_workers:
                print(f"{reg_num}: {last_name} {first_name}")
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
    finally:
        conn.close()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔄 ترحيل قاعدة البيانات - إضافة معلومات العامل الجديدة")
    print("=" * 60)
    
    # إنشاء نسخة احتياطية
    if backup_database():
        print("✅ تم إنشاء نسخة احتياطية")
    
    # إضافة الحقول الجديدة
    if add_new_fields():
        print("✅ تم إضافة الحقول الجديدة")
        
        # توليد أرقام التسجيل
        generate_registration_numbers()
        
        # التحقق من النتيجة
        verify_migration()
        
        print("\n🎉 تم ترحيل قاعدة البيانات بنجاح!")
    else:
        print("❌ فشل في ترحيل قاعدة البيانات")

if __name__ == "__main__":
    main()
