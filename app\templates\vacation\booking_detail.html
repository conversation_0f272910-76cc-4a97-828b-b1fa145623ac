{% extends "base.html" %}

{% block title %}تفاصيل الحجز - {{ booking.booking_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <!-- معلومات الحجز -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-check text-primary"></i>
                        تفاصيل الحجز - {{ booking.booking_number }}
                    </h3>
                    <div class="card-tools">
                        {% if booking.status == 'confirmed' %}
                            <span class="badge badge-success badge-lg">{{ booking.status_display }}</span>
                        {% elif booking.status == 'completed' %}
                            <span class="badge badge-info badge-lg">{{ booking.status_display }}</span>
                        {% elif booking.status == 'cancelled' %}
                            <span class="badge badge-danger badge-lg">{{ booking.status_display }}</span>
                        {% endif %}
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">رقم الحجز:</th>
                                    <td><strong class="text-primary">{{ booking.booking_number }}</strong></td>
                                </tr>
                                <tr>
                                    <th>العامل:</th>
                                    <td>
                                        <a href="{{ url_for('main.worker_detail', id=booking.worker.id) }}" class="text-decoration-none">
                                            {{ booking.worker.full_name }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <th>الشقة:</th>
                                    <td>{{ booking.apartment.full_name }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ البداية:</th>
                                    <td>{{ booking.start_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <th>تاريخ النهاية:</th>
                                    <td>{{ booking.end_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">المدة:</th>
                                    <td>{{ booking.duration_days }} يوم</td>
                                </tr>
                                <tr>
                                    <th>المبلغ الإجمالي:</th>
                                    <td><strong class="text-success">{{ booking.formatted_total_amount }}</strong></td>
                                </tr>
                                <tr>
                                    <th>المبلغ المدفوع:</th>
                                    <td><strong class="text-info">{{ "{:,.2f}".format(booking.total_paid) }} دج</strong></td>
                                </tr>
                                <tr>
                                    <th>المبلغ المتبقي:</th>
                                    <td><strong class="text-warning">{{ "{:,.2f}".format(booking.remaining_amount) }} دج</strong></td>
                                </tr>
                                <tr>
                                    <th>تاريخ الحجز:</th>
                                    <td>{{ booking.booking_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    {% if booking.notes %}
                    <div class="alert alert-info">
                        <h6><i class="fas fa-sticky-note"></i> ملاحظات:</h6>
                        <p class="mb-0">{{ booking.notes }}</p>
                    </div>
                    {% endif %}
                </div>

                <div class="card-footer">
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('main.vacation_bookings_list') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                        {% if booking.status != 'cancelled' %}
                        <a href="{{ url_for('main.edit_vacation_booking', id=booking.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> تعديل الحجز
                        </a>
                        {% endif %}
                        {% if booking.status == 'confirmed' %}
                        <form method="POST" action="{{ url_for('main.cancel_vacation_booking', id=booking.id) }}" style="display: inline;">
                            <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من إلغاء الحجز؟')">
                                <i class="fas fa-times"></i> إلغاء الحجز
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- جدول الدفعات -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-money-bill-wave text-success"></i>
                        جدول الدفعات
                    </h4>
                </div>

                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="thead-light">
                                <tr>
                                    <th>رقم الدفعة</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td><small>{{ payment.payment_number }}</small></td>
                                    <td>{{ payment.formatted_amount }}</td>
                                    <td>{{ payment.due_date.strftime('%m-%d') }}</td>
                                    <td>
                                        {% if payment.status == 'paid' %}
                                            <span class="badge badge-success">{{ payment.status_display }}</span>
                                        {% elif payment.status == 'pending' %}
                                            <span class="badge badge-warning">{{ payment.status_display }}</span>
                                        {% elif payment.status == 'overdue' %}
                                            <span class="badge badge-danger">{{ payment.status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.status == 'pending' %}
                                        <button class="btn btn-xs btn-success" onclick="markAsPaid({{ payment.id }})">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">
                                        لا توجد دفعات
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                {% if booking.status != 'cancelled' %}
                <div class="card-footer">
                    <button class="btn btn-success btn-sm btn-block" onclick="addPayment()">
                        <i class="fas fa-plus"></i> إضافة دفعة
                    </button>
                </div>
                {% endif %}
            </div>

            <!-- معلومات الشقة -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-home text-info"></i>
                        معلومات الشقة
                    </h4>
                </div>

                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <th>العمارة:</th>
                            <td>{{ booking.apartment.building.name }}</td>
                        </tr>
                        <tr>
                            <th>الموقع:</th>
                            <td>{{ booking.apartment.building.location }}</td>
                        </tr>
                        <tr>
                            <th>الطابق:</th>
                            <td>{{ booking.apartment.floor_number }}</td>
                        </tr>
                        <tr>
                            <th>رقم الشقة:</th>
                            <td>{{ booking.apartment.apartment_number }}</td>
                        </tr>
                        <tr>
                            <th>السعة:</th>
                            <td>{{ booking.apartment.capacity }} أشخاص</td>
                        </tr>
                        <tr>
                            <th>السعر الأسبوعي:</th>
                            <td>{{ booking.apartment.formatted_price }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function markAsPaid(paymentId) {
    if (confirm('هل تم دفع هذه الدفعة؟')) {
        // إرسال طلب AJAX لتحديث حالة الدفعة
        fetch(`/vacation_payment/${paymentId}/mark_paid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function addPayment() {
    // فتح نافذة إضافة دفعة
    window.location.href = `{{ url_for('main.add_vacation_payment', booking_id=booking.id) }}`;
}
</script>
{% endblock %}
