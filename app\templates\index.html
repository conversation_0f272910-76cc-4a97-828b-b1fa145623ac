{% extends "base.html" %}

{% block title %}الرئيسية - لجنة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4 text-center">مرحباً بكم في نظام إدارة لجنة الخدمات الاجتماعية</h1>
            <p class="lead text-center">ولاية الجلفة</p>
            <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
            <p class="text-center">نظام شامل لإدارة بيانات العمال والخدمات الاجتماعية</p>
        </div>
    </div>
</div>

<div class="row">
    <!-- إحصائيات -->
    <div class="col-md-4 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-3x text-primary mb-3"></i>
                <h5 class="card-title">إجمالي العمال</h5>
                <h2 class="text-primary">{{ total_workers }}</h2>
                <a href="{{ url_for('main.workers_list') }}" class="btn btn-outline-primary">عرض القائمة</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-child fa-3x text-success mb-3"></i>
                <h5 class="card-title">إجمالي الأطفال</h5>
                <h2 class="text-success">{{ total_children }}</h2>
                <a href="/children_stats" class="btn btn-outline-success">عرض الإحصائيات</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-venus fa-3x text-danger mb-3"></i>
                <h5 class="card-title">عدد النساء</h5>
                <h2 class="text-danger">{{ female_workers }}</h2>
                <a href="/female_workers" class="btn btn-outline-danger">عرض القائمة</a>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات نظام الاصطياف -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="text-muted mb-3">
            <i class="fas fa-umbrella-beach me-2"></i>نظام الاصطياف
        </h4>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="fas fa-building fa-3x text-info mb-3"></i>
                <h5 class="card-title">العمارات</h5>
                <h2 class="text-info">{{ total_buildings }}</h2>
                <a href="{{ url_for('main.vacation_buildings_list') }}" class="btn btn-outline-info">إدارة العمارات</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-home fa-3x text-warning mb-3"></i>
                <h5 class="card-title">الشقق</h5>
                <h2 class="text-warning">{{ total_apartments }}</h2>
                <a href="{{ url_for('main.vacation_apartments_list') }}" class="btn btn-outline-warning">إدارة الشقق</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="fas fa-calendar-check fa-3x text-success mb-3"></i>
                <h5 class="card-title">الحجوزات</h5>
                <h2 class="text-success">{{ total_bookings }}</h2>
                <a href="{{ url_for('main.vacation_bookings_list') }}" class="btn btn-outline-success">عرض الحجوزات</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-check-circle fa-3x text-primary mb-3"></i>
                <h5 class="card-title">حجوزات مؤكدة</h5>
                <h2 class="text-primary">{{ confirmed_bookings }}</h2>
                <a href="{{ url_for('main.vacation_reports') }}" class="btn btn-outline-primary">عرض التقارير</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الإجراءات السريعة -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('main.add_worker') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus me-2"></i>إضافة عامل جديد
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('main.workers_list') }}" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-search me-2"></i>البحث في قائمة العمال
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('main.thermal_receipts_list') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-receipt me-2"></i>وصولات الحمام المعدني
                        </a>
                    </div>
                </div>

                <!-- نظام الاصطياف -->
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-umbrella-beach me-2"></i>نظام الاصطياف
                        </h6>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('main.vacation_buildings_list') }}" class="btn btn-outline-info btn-lg w-100">
                            <i class="fas fa-building me-2"></i>إدارة العمارات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('main.vacation_apartments_list') }}" class="btn btn-outline-info btn-lg w-100">
                            <i class="fas fa-home me-2"></i>إدارة الشقق
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('main.vacation_bookings_list') }}" class="btn btn-outline-warning btn-lg w-100">
                            <i class="fas fa-calendar-check me-2"></i>الحجوزات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('main.vacation_reports') }}" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if recent_workers %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>آخر العمال المضافين
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم الكامل</th>
                                <th>المنصب</th>
                                <th>مكان العمل</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for worker in recent_workers %}
                            <tr>
                                <td>
                                    {% if worker.photo_filename %}
                                        <img src="{{ url_for('static', filename='uploads/' + worker.photo_filename) }}" 
                                             alt="صورة {{ worker.full_name }}" class="worker-photo">
                                    {% else %}
                                        <div class="worker-photo bg-secondary d-flex align-items-center justify-content-center">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>{{ worker.full_name }}</td>
                                <td>{{ worker.position }}</td>
                                <td>{{ worker.workplace }}</td>
                                <td>{{ worker.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('main.worker_detail', id=worker.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
