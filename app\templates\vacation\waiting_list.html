{% extends "base.html" %}

{% block title %}قائمة الاحتياط للاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-list-alt text-warning"></i>
                        قائمة الاحتياط للاصطياف
                    </h3>
                    <a href="{{ url_for('main.add_vacation_waiting_list') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة لقائمة الاحتياط
                    </a>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-list-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي القائمة</span>
                                    <span class="info-box-number">{{ total_waiting }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">في الانتظار</span>
                                    <span class="info-box-number">{{ waiting_count }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-bell"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">تم الإشعار</span>
                                    <span class="info-box-number">{{ notified_count }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-times-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">منتهي الصلاحية</span>
                                    <span class="info-box-number">{{ expired_count }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلترة -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="worker_name" class="form-control" 
                                       placeholder="اسم العامل" value="{{ worker_name }}">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-control">
                                    <option value="">جميع الحالات</option>
                                    <option value="waiting" {% if status == 'waiting' %}selected{% endif %}>في الانتظار</option>
                                    <option value="notified" {% if status == 'notified' %}selected{% endif %}>تم الإشعار</option>
                                    <option value="expired" {% if status == 'expired' %}selected{% endif %}>منتهي الصلاحية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="apartment_id" class="form-control">
                                    <option value="">جميع الشقق</option>
                                    {% for apartment in apartments %}
                                    <option value="{{ apartment.id }}" {% if apartment_id == apartment.id %}selected{% endif %}>
                                        {{ apartment.full_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ url_for('main.vacation_waiting_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> مسح الفلتر
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>الأولوية</th>
                                    <th>العامل</th>
                                    <th>الشقة المرغوبة</th>
                                    <th>الفترة المفضلة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>تاريخ الإشعار</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in waiting_list.items %}
                                <tr>
                                    <td>
                                        <span class="badge badge-primary">{{ entry.priority }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('main.worker_detail', id=entry.worker.id) }}" class="text-decoration-none">
                                            {{ entry.worker.full_name }}
                                        </a>
                                    </td>
                                    <td>{{ entry.apartment.full_name }}</td>
                                    <td>
                                        <small>
                                            من {{ entry.preferred_start_date.strftime('%Y-%m-%d') }}<br>
                                            إلى {{ entry.preferred_end_date.strftime('%Y-%m-%d') }}
                                        </small>
                                    </td>
                                    <td>{{ entry.registration_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if entry.notification_date %}
                                            {{ entry.notification_date.strftime('%Y-%m-%d') }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if entry.status == 'waiting' %}
                                            <span class="badge badge-warning">{{ entry.status_display }}</span>
                                        {% elif entry.status == 'notified' %}
                                            <span class="badge badge-success">{{ entry.status_display }}</span>
                                        {% elif entry.status == 'expired' %}
                                            <span class="badge badge-danger">{{ entry.status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            {% if entry.status == 'waiting' %}
                                            <button class="btn btn-sm btn-success" onclick="updateStatus({{ entry.id }}, 'notified')" title="إشعار">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                            {% endif %}
                                            {% if entry.status != 'expired' %}
                                            <button class="btn btn-sm btn-danger" onclick="updateStatus({{ entry.id }}, 'expired')" title="انتهاء الصلاحية">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteEntry({{ entry.id }})" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد عناصر في قائمة الاحتياط
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    {% if waiting_list.pages > 1 %}
                    <nav aria-label="تصفح الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if waiting_list.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_waiting_list', page=waiting_list.prev_num, worker_name=worker_name, status=status, apartment_id=apartment_id) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in waiting_list.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != waiting_list.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.vacation_waiting_list', page=page_num, worker_name=worker_name, status=status, apartment_id=apartment_id) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if waiting_list.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_waiting_list', page=waiting_list.next_num, worker_name=worker_name, status=status, apartment_id=apartment_id) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateStatus(entryId, newStatus) {
    var confirmMessage = '';
    if (newStatus === 'notified') {
        confirmMessage = 'هل تريد إشعار هذا العامل؟';
    } else if (newStatus === 'expired') {
        confirmMessage = 'هل تريد تحديد هذا العنصر كمنتهي الصلاحية؟';
    }
    
    if (confirm(confirmMessage)) {
        fetch(`/vacation_waiting_list/${entryId}/update_status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({status: newStatus})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function deleteEntry(entryId) {
    if (confirm('هل أنت متأكد من حذف هذا العنصر من قائمة الاحتياط؟')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = `/vacation_waiting_list/${entryId}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
