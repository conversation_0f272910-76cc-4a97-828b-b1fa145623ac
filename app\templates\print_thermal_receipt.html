<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وصل استلام خاص بالحمام المعدني - {{ receipt.receipt_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

        body {
            font-family: '<PERSON><PERSON>', serif;
            background-color: white;
            margin: 0;
            padding: 2mm 5mm 2mm 5mm; /* تقليل الهوامش العلوية والسفلية */
            direction: rtl;
            font-size: 20px;
            line-height: 1.2;
        }

        .page-container {
            width: 180mm;
            max-width: 180mm;
            margin: 0 auto;
        }

        .receipt-container {
            width: 100%;
            background: white;
            border: 2px solid #000;
            padding: 3mm 3mm;
            margin-bottom: 5mm; /* مسافة 0.5 سم بين الوصلين */
            position: relative;
            page-break-inside: avoid;
            min-height: 85mm;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #000;
            padding-bottom: 2mm;
            margin-bottom: 2mm;
        }

        .header-center {
            flex: 1;
            text-align: center;
            margin: 0 2mm;
        }

        .logo {
            width: 24mm;
            height: 24mm;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .logo-placeholder {
            width: 24mm;
            height: 24mm;
            border: 1px dashed #ccc;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #999;
            text-align: center;
        }

        .header h1 {
            font-size: 20px;
            margin: 0.3mm 0;
            font-weight: bold;
        }

        .header h2 {
            font-size: 18px;
            margin: 0.3mm 0;
            font-weight: bold;
        }

        .header h3 {
            font-size: 16px;
            margin: 0.2mm 0;
        }

        .receipt-title {
            text-align: center;
            margin: 1.5mm 0;
            font-size: 20px;
            font-weight: bold;
            border: 2px solid #000;
            padding: 2mm 3mm;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 6px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: relative;
        }

        .receipt-title::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #007bff, #0056b3, #007bff);
            border-radius: 10px;
            z-index: -1;
        }

        .receipt-subtitle {
            text-align: center;
            margin: 1.5mm 0;
            font-size: 20px;
        }

        .receipt-number {
            text-align: right;
            margin: 1.5mm 0;
            font-size: 20px;
            font-weight: bold;
        }

        .info-section {
            margin: 2mm 0;
        }

        .info-row {
            display: flex;
            margin: 1.5mm 0;
            align-items: center;
        }

        .info-label {
            font-weight: bold;
            margin-left: 2mm;
            min-width: 25mm;
            font-size: 20px;
        }

        .info-value {
            border-bottom: 1px solid #000;
            flex: 1;
            padding: 1mm 1.5mm;
            min-height: 4mm;
            font-size: 20px;
        }

        .signature-section {
            margin-top: 4mm;
            text-align: left;
        }

        .date-location {
            margin: 2mm 0;
            text-align: left;
            font-size: 20px;
        }

        .signature-title {
            margin-top: 2mm;
            font-weight: bold;
            font-size: 20px;
            margin-bottom: 30mm; /* مساحة 3 سم للتوقيع */
        }

        @media print {
            body {
                background-color: white;
                padding: 2mm 10mm 2mm 10mm; /* تقليل الهوامش العلوية والسفلية في الطباعة */
                margin: 0;
                font-size: 12px;
            }

            .page-container {
                width: 100%;
                max-width: 180mm;
                margin: 0 auto;
            }

            .receipt-container {
                border: 2px solid #000;
                margin-bottom: 5mm; /* مسافة 0.5 سم بين الوصلين في الطباعة */
                page-break-inside: avoid;
                padding: 3mm 3mm;
            }

            .no-print {
                display: none;
            }

            /* ضمان عدم تجاوز العرض */
            * {
                max-width: 100%;
                box-sizing: border-box;
            }

            /* مساحة التوقيع في الطباعة */
            .signature-title {
                margin-bottom: 25mm; /* مساحة أقل قليلاً في الطباعة */
            }
        }

        /* ضمان عدم تجاوز العرض في جميع الأحوال */
        * {
            box-sizing: border-box;
        }

        .page-container,
        .receipt-container,
        .header,
        .info-row {
            max-width: 100%;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- الوصل الأول -->
        <div class="receipt-container">
            <!-- Header -->
            <div class="header">
                <!-- الشعار الأيسر -->
                <div class="logo">
                    {% if logo_exists %}
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الولاية">
                    {% else %}
                        <div class="logo-placeholder">شعار</div>
                    {% endif %}
                </div>

                <!-- العنوان الرئيسي -->
                <div class="header-center">
                    <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                    <h2>ولاية الجلفة</h2>
                    <h3>مديرية الإدارة المحلية</h3>
                    <h3>لجنة الخدمات الاجتماعية لمقر الولاية و الدوائر</h3>
                </div>

                <!-- الشعار الأيمن -->
                <div class="logo">
                    {% if logo_exists %}
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الولاية">
                    {% else %}
                        <div class="logo-placeholder">شعار</div>
                    {% endif %}
                </div>
            </div>

            <!-- رقم الوصل -->
            <div class="receipt-number">
                الرقم: {{ receipt.receipt_number }}
            </div>

            <!-- عنوان الوصل -->
            <div class="receipt-title">
                وصل استلام خاص بالحمام المعدني
            </div>

            <div class="receipt-subtitle">
                {{ receipt.treatment_location }} المبلغ ({{ "{:,.0f}".format(receipt.worker_payment) }} دج)
            </div>

            <!-- معلومات الموظف -->
            <div class="info-section">
                <div class="info-row">
                    <span class="info-label">الاسم و اللقب:</span>
                    <div class="info-value">{{ receipt.worker.last_name }} {{ receipt.worker.first_name if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">التعيين:</span>
                    <div class="info-value">{{ receipt.worker.position if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">الفوج:</span>
                    <div class="info-value">{{ receipt.worker.workplace if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <div class="info-value">{{ receipt.worker.phone if receipt.worker else '' }}</div>
                </div>
            </div>

            <!-- التاريخ والمكان -->
            <div class="date-location">
                الجلفة في {{ receipt.issue_date.strftime('%Y/%m/%d') if receipt.issue_date else '____/__/__' }}
            </div>

            <!-- التوقيع -->
            <div class="signature-section">
                <div class="signature-title">نائب رئيس لجنة الخدمات</div>
            </div>
        </div>

        <!-- الوصل الثاني (نسخة مطابقة) -->
        <div class="receipt-container">
            <!-- Header -->
            <div class="header">
                <!-- الشعار الأيسر -->
                <div class="logo">
                    {% if logo_exists %}
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الولاية">
                    {% else %}
                        <div class="logo-placeholder">شعار</div>
                    {% endif %}
                </div>

                <!-- العنوان الرئيسي -->
                <div class="header-center">
                    <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                    <h2>ولاية الجلفة</h2>
                    <h3>مديرية الإدارة المحلية</h3>
                    <h3>لجنة الخدمات الاجتماعية لمقر الولاية و الدوائر</h3>
                </div>

                <!-- الشعار الأيمن -->
                <div class="logo">
                    {% if logo_exists %}
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الولاية">
                    {% else %}
                        <div class="logo-placeholder">شعار</div>
                    {% endif %}
                </div>
            </div>

            <!-- رقم الوصل -->
            <div class="receipt-number">
                الرقم: {{ receipt.receipt_number }}
            </div>

            <!-- عنوان الوصل -->
            <div class="receipt-title">
                وصل استلام خاص بالحمام المعدني
            </div>

            <div class="receipt-subtitle">
                {{ receipt.treatment_location }} المبلغ ({{ "{:,.0f}".format(receipt.worker_payment) }} دج)
            </div>

            <!-- معلومات الموظف -->
            <div class="info-section">
                <div class="info-row">
                    <span class="info-label">الاسم و اللقب:</span>
                    <div class="info-value">{{ receipt.worker.last_name }} {{ receipt.worker.first_name if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">التعيين:</span>
                    <div class="info-value">{{ receipt.worker.position if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">الفوج:</span>
                    <div class="info-value">{{ receipt.worker.workplace if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <div class="info-value">{{ receipt.worker.phone if receipt.worker else '' }}</div>
                </div>
            </div>

            <!-- التاريخ والمكان -->
            <div class="date-location">
                الجلفة في {{ receipt.issue_date.strftime('%Y/%m/%d') if receipt.issue_date else '____/__/__' }}
            </div>

            <!-- التوقيع -->
            <div class="signature-section">
                <div class="signature-title">نائب رئيس لجنة الخدمات</div>
            </div>
        </div>
    </div>

    <!-- أزرار الطباعة والعودة -->
    <div style="text-align: center; margin-top: 20px;" class="no-print">
        <button onclick="window.print()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">
            طباعة الوصل
        </button>
        <a href="{{ url_for('main.thermal_receipts_list') }}" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;">
            العودة للقائمة
        </a>
    </div>
</body>
</html>
