#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ملف التشغيل البديل لتطبيق لجنة الخدمات الاجتماعية
"""

import os
import sys
import webbrowser
import threading
import time

# تعيين ترميز UTF-8
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from app import create_app, db
except ImportError as e:
    print(f"Error importing app: {e}")
    print("Make sure app folder and required files exist")
    input("Press Enter to exit...")
    sys.exit(1)

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(2)
    webbrowser.open('http://127.0.0.1:5000')

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("Workers Social Services Committee Application")
    print("New Vacation System")
    print("=" * 60)
    print("Starting application...")

    try:
        # إنشاء التطبيق
        app = create_app()
        print("Application created successfully")

        # إنشاء الجداول
        with app.app_context():
            db.create_all()
            print("Database tables created")

        # تحديد المنفذ
        port = 5000

        print(f"Application started successfully!")
        print(f"Application URL: http://127.0.0.1:{port}")
        print(f"Browser will open automatically...")
        print("=" * 60)
        print("Instructions:")
        print("   - Application will open in browser automatically")
        print("   - Press Ctrl+C to stop the application")
        print("   - Don't close this window while using the application")
        print("=" * 60)
        print("New Features:")
        print("   - Buildings and apartments management")
        print("   - Booking system (coming soon)")
        print("   - Payment system (coming soon)")
        print("   - Waiting list (coming soon)")
        print("=" * 60)

        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()

        # تشغيل التطبيق
        app.run(
            host='127.0.0.1',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )

    except KeyboardInterrupt:
        print("\nApplication stopped by user")
    except Exception as e:
        print(f"Error running application: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
    finally:
        print("Thank you for using the application!")

if __name__ == '__main__':
    main()
