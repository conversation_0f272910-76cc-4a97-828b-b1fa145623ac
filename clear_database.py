#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف لحذف جميع البيانات من قاعدة البيانات
"""

from app import create_app, db
from app.models import Worker, Child

def clear_all_data():
    """حذف جميع البيانات من قاعدة البيانات"""
    app = create_app()
    
    with app.app_context():
        # حذف جميع البيانات
        Child.query.delete()
        Worker.query.delete()
        db.session.commit()
        
        print("تم حذف جميع البيانات من قاعدة البيانات بنجاح!")
        print("قاعدة البيانات الآن فارغة وجاهزة للاستخدام الحقيقي.")
        
        # عرض إحصائيات للتأكد
        total_workers = Worker.query.count()
        total_children = Child.query.count()
        
        print(f"إجمالي العمال: {total_workers}")
        print(f"إجمالي الأطفال: {total_children}")

if __name__ == '__main__':
    clear_all_data()
