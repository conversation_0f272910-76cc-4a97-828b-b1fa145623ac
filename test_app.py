#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("Testing Python environment...")

try:
    import flask
    print(f"✅ Flask version: {flask.__version__}")
except ImportError as e:
    print(f"❌ Flask import error: {e}")

try:
    import sqlalchemy
    print(f"✅ SQLAlchemy version: {sqlalchemy.__version__}")
except ImportError as e:
    print(f"❌ SQLAlchemy import error: {e}")

try:
    from flask import Flask
    from flask_sqlalchemy import SQLAlchemy
    
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db = SQLAlchemy(app)
    
    print("✅ Basic Flask app created successfully")
    
    @app.route('/')
    def hello():
        return "Hello World! Flask is working!"
    
    print("✅ Route defined successfully")
    
    if __name__ == '__main__':
        print("Starting Flask development server...")
        app.run(debug=True, port=5001)
        
except Exception as e:
    print(f"❌ Error creating Flask app: {e}")
    import traceback
    traceback.print_exc()
