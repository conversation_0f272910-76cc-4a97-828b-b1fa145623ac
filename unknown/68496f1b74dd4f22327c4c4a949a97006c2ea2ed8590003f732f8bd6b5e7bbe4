{% extends "base.html" %}

{% block title %}تفاصيل الوصل {{ receipt.receipt_number }} - لجنة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-receipt me-2"></i>
                    تفاصيل الوصل رقم {{ receipt.receipt_number }}
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('main.print_thermal_receipt', id=receipt.id) }}" 
                       class="btn btn-success" target="_blank">
                        <i class="fas fa-print me-1"></i>طباعة
                    </a>
                    <a href="{{ url_for('main.edit_thermal_receipt', id=receipt.id) }}" 
                       class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </a>
                    <a href="{{ url_for('main.thermal_receipts_list') }}" 
                       class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- معلومات الوصل -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>معلومات الوصل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">رقم الوصل:</td>
                                            <td>{{ receipt.receipt_number }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">تاريخ الإصدار:</td>
                                            <td>{{ receipt.issue_date.strftime('%Y-%m-%d') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">مكان العلاج:</td>
                                            <td>{{ receipt.treatment_location }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">المبلغ الإجمالي:</td>
                                            <td>
                                                <span class="badge bg-info fs-6">
                                                    {{ "{:,.0f}".format(receipt.total_amount) }} د.ج
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">مساهمة اللجنة:</td>
                                            <td>
                                                <span class="badge bg-success fs-6">
                                                    {{ "{:,.0f}".format(receipt.committee_contribution) }} د.ج
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">دفع العامل:</td>
                                            <td>
                                                <span class="badge bg-warning text-dark fs-6">
                                                    {{ "{:,.0f}".format(receipt.worker_payment) }} د.ج
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            {% if receipt.notes %}
                            <div class="mt-3">
                                <h6 class="fw-bold">ملاحظات:</h6>
                                <div class="bg-light p-3 rounded">
                                    {{ receipt.notes }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- معلومات العامل -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>معلومات العامل
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            {% if receipt.worker.photo_filename %}
                            <img src="{{ url_for('static', filename='uploads/' + receipt.worker.photo_filename) }}" 
                                 alt="صورة {{ receipt.worker.full_name }}" 
                                 class="worker-photo mb-3">
                            {% else %}
                            <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                                 style="width: 80px; height: 80px;">
                                <i class="fas fa-user fa-2x text-muted"></i>
                            </div>
                            {% endif %}
                            
                            <h5 class="card-title">{{ receipt.worker.full_name }}</h5>
                            
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td class="fw-bold">المنصب:</td>
                                    <td>{{ receipt.worker.position or 'غير محدد' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">مكان العمل:</td>
                                    <td>{{ receipt.worker.workplace or 'غير محدد' }}</td>
                                </tr>
                                {% if receipt.worker.phone %}
                                <tr>
                                    <td class="fw-bold">الهاتف:</td>
                                    <td>{{ receipt.worker.phone }}</td>
                                </tr>
                                {% endif %}
                            </table>
                            
                            <a href="{{ url_for('main.worker_detail', id=receipt.worker.id) }}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>عرض ملف العامل
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>إحصائيات العامل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-primary">{{ receipt.worker.thermal_receipts|length }}</h4>
                                        <small class="text-muted">إجمالي الوصولات</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-success">
                                            {{ "{:,.0f}".format(receipt.worker.thermal_receipts|sum(attribute='committee_contribution')) }}
                                        </h4>
                                        <small class="text-muted">إجمالي مساهمة اللجنة (د.ج)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-warning">
                                            {{ "{:,.0f}".format(receipt.worker.thermal_receipts|sum(attribute='worker_payment')) }}
                                        </h4>
                                        <small class="text-muted">إجمالي دفع العامل (د.ج)</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="border rounded p-3">
                                        <h4 class="text-info">
                                            {{ "{:,.0f}".format(receipt.worker.thermal_receipts|sum(attribute='total_amount')) }}
                                        </h4>
                                        <small class="text-muted">إجمالي المبالغ (د.ج)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- وصولات أخرى للعامل -->
            {% if receipt.worker.thermal_receipts|length > 1 %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>وصولات أخرى للعامل
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>رقم الوصل</th>
                                            <th>تاريخ الإصدار</th>
                                            <th>المبلغ الإجمالي</th>
                                            <th>مساهمة اللجنة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for other_receipt in receipt.worker.thermal_receipts %}
                                            {% if other_receipt.id != receipt.id %}
                                            <tr>
                                                <td>{{ other_receipt.receipt_number }}</td>
                                                <td>{{ other_receipt.issue_date.strftime('%Y-%m-%d') }}</td>
                                                <td>{{ "{:,.0f}".format(other_receipt.total_amount) }} د.ج</td>
                                                <td>{{ "{:,.0f}".format(other_receipt.committee_contribution) }} د.ج</td>
                                                <td>
                                                    <a href="{{ url_for('main.thermal_receipt_detail', id=other_receipt.id) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
