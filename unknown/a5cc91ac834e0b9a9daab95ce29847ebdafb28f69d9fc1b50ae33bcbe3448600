#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص الجداول في قاعدة البيانات
"""

import sqlite3
import os

def check_tables():
    """فحص الجداول في قاعدة البيانات"""
    db_path = 'instance/workers_committee.db'
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # عرض جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("الجداول الموجودة في قاعدة البيانات:")
        print("-" * 50)
        for table in tables:
            print(f"جدول: {table[0]}")
            
            # عرض هيكل كل جدول
            cursor.execute(f"PRAGMA table_info({table[0]})")
            columns = cursor.fetchall()
            
            print(f"  أعمدة جدول {table[0]}:")
            for column in columns:
                print(f"    - {column[1]} ({column[2]})")
            print()
            
    except Exception as e:
        print(f"خطأ في فحص قاعدة البيانات: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_tables()
