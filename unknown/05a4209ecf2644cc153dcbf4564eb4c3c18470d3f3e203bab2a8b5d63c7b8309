{% extends "base.html" %}

{% block title %}إعدادات الشعار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-image me-2"></i>إعدادات الشعار والخلفية المائية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- رفع الشعار -->
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-upload me-1"></i>رفع شعار جديد
                        </h6>
                        
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() if csrf_token else '' }}"/>
                            
                            <div class="mb-3">
                                <label class="form-label" for="logo">اختر ملف الشعار</label>
                                <input type="file" class="form-control" id="logo" name="logo" 
                                       accept="image/*" required>
                                <div class="form-text">
                                    الصيغ المدعومة: PNG, JPG, GIF<br>
                                    يفضل استخدام صور شفافة (PNG) للحصول على أفضل نتيجة
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>حفظ الشعار
                            </button>
                        </form>
                    </div>
                    
                    <!-- معاينة الشعار الحالي -->
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-eye me-1"></i>الشعار الحالي
                        </h6>
                        
                        {% if logo_exists %}
                            <div class="text-center">
                                <img src="{{ url_for('static', filename='images/logo.png') }}?v={{ range(1000, 9999) | random }}" 
                                     alt="الشعار الحالي" 
                                     class="img-fluid border rounded p-3"
                                     style="max-width: 200px; max-height: 200px;">
                                <p class="text-muted mt-2">الشعار الحالي</p>
                            </div>
                        {% else %}
                            <div class="text-center text-muted">
                                <i class="fas fa-image fa-4x mb-3 opacity-50"></i>
                                <p>لم يتم رفع شعار بعد</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <hr>
                
                <!-- معاينة الخلفية المائية -->
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-magic me-1"></i>معاينة الخلفية المائية
                        </h6>
                        
                        <div class="row">
                            <!-- معاينة عادية -->
                            <div class="col-md-6">
                                <div class="card watermark-enabled" style="min-height: 300px;">
                                    <div class="card-header">
                                        <h6 class="mb-0">معاينة الخلفية المائية - عادي</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>هذا نص تجريبي لمعاينة كيف ستظهر الخلفية المائية في الصفحات العادية.</p>
                                        <p>الشعار يظهر بشفافية خفيفة في الخلفية دون أن يؤثر على قراءة النص.</p>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>البيان</th>
                                                        <th>القيمة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>الشفافية</td>
                                                        <td>4%</td>
                                                    </tr>
                                                    <tr>
                                                        <td>الحجم</td>
                                                        <td>350x350 بكسل</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- معاينة للطباعة -->
                            <div class="col-md-6">
                                <div class="card print-watermark" style="min-height: 300px;">
                                    <div class="card-header">
                                        <h6 class="mb-0">معاينة الخلفية المائية - طباعة</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>هذا نص تجريبي لمعاينة كيف ستظهر الخلفية المائية عند الطباعة.</p>
                                        <p>الشعار يظهر بشفافية أعلى قليلاً للطباعة الواضحة.</p>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>البيان</th>
                                                        <th>القيمة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>الشفافية</td>
                                                        <td>8%</td>
                                                    </tr>
                                                    <tr>
                                                        <td>الحجم</td>
                                                        <td>500x500 بكسل</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <!-- تعليمات الاستخدام -->
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle me-1"></i>تعليمات الاستخدام
                        </h6>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-1"></i>نصائح للحصول على أفضل نتيجة:</h6>
                            <ul class="mb-0">
                                <li><strong>استخدم صور PNG شفافة:</strong> للحصول على خلفية مائية نظيفة</li>
                                <li><strong>الحجم المناسب:</strong> 500x500 بكسل أو أكبر للوضوح</li>
                                <li><strong>الألوان:</strong> تجنب الألوان الداكنة جداً أو الفاتحة جداً</li>
                                <li><strong>البساطة:</strong> الشعارات البسيطة تعطي نتائج أفضل</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-1"></i>ملاحظات مهمة:</h6>
                            <ul class="mb-0">
                                <li>الخلفية المائية ستظهر في جميع صفحات النظام</li>
                                <li>يمكن تغيير الشعار في أي وقت</li>
                                <li>الشعار الجديد سيحل محل الشعار القديم</li>
                                <li>تأكد من حقوق استخدام الشعار قبل رفعه</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار التحكم -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>العودة للرئيسية
                            </a>
                            
                            {% if logo_exists %}
                                <button type="button" class="btn btn-outline-primary" onclick="testPrint()">
                                    <i class="fas fa-print me-1"></i>اختبار الطباعة
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testPrint() {
    // إنشاء نافذة جديدة لاختبار الطباعة
    var printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>اختبار الطباعة - الخلفية المائية</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
            <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
            <style>
                body { font-family: 'Cairo', sans-serif; }
                .test-content { padding: 2rem; min-height: 80vh; }
            </style>
        </head>
        <body class="watermark-enabled">
            <div class="container test-content">
                <h1 class="text-center mb-4">اختبار الخلفية المائية للطباعة</h1>
                <p>هذا نص تجريبي لاختبار كيف ستظهر الخلفية المائية عند الطباعة.</p>
                <p>يجب أن يظهر الشعار في الخلفية بشفافية مناسبة دون أن يؤثر على وضوح النص.</p>
                
                <table class="table table-bordered mt-4">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>التفاصيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>اسم النظام</td>
                            <td>نظام إدارة لجنة الخدمات الاجتماعية</td>
                        </tr>
                        <tr>
                            <td>الولاية</td>
                            <td>الجلفة</td>
                        </tr>
                        <tr>
                            <td>تاريخ الطباعة</td>
                            <td>${new Date().toLocaleDateString('ar-DZ')}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="mt-5">
                    <p><strong>ملاحظة:</strong> هذه صفحة اختبار للتأكد من ظهور الخلفية المائية بشكل صحيح عند الطباعة.</p>
                </div>
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    
    // انتظار تحميل الصفحة ثم طباعة
    setTimeout(() => {
        printWindow.print();
    }, 1000);
}
</script>
{% endblock %}
