#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from datetime import datetime

# إنشاء بيانات نموذجية للعمال
data = {
    'الاسم': [
        'أحمد', 'فاطمة', 'محمد', 'عائشة', 'يوسف', 
        'خديجة', 'عبد الرحمن', 'زينب', 'إبراهيم', 'مريم'
    ],
    'اللقب': [
        'بن علي', 'بن محمد', 'قاسمي', 'العربي', 'بن سالم',
        'بوعلام', 'الجزائري', 'بن عيسى', 'حمادي', 'بن يوسف'
    ],
    'تاريخ_الميلاد': [
        '1985-03-15', '1990-07-22', '1988-12-10', '1992-05-18', '1987-09-03',
        '1989-11-25', '1986-01-30', '1991-08-14', '1984-06-07', '1993-04-12'
    ],
    'مكان_الميلاد': [
        'الجلفة', 'حاسي بحبح', 'عين الإبل', 'الجلفة', 'حاسي العش',
        'الجلفة', 'عين الإبل', 'حاسي بحبح', 'الجلفة', 'حاسي العش'
    ],
    'الوظيفة': [
        'مهندس', 'معلمة', 'طبيب', 'ممرضة', 'محاسب',
        'مديرة', 'تقني', 'صيدلانية', 'مهندس', 'معلمة'
    ],
    'المنصب': [
        'مهندس أول', 'معلمة رئيسية', 'طبيب عام', 'ممرضة رئيسية', 'محاسب أول',
        'مديرة إدارية', 'تقني سامي', 'صيدلانية رئيسية', 'مهندس رئيسي', 'معلمة أولى'
    ],
    'مكان_العمل': [
        'مديرية التربية', 'ابتدائية النور', 'مستشفى الجلفة', 'مستشفى الجلفة', 'بلدية الجلفة',
        'مديرية الصحة', 'مديرية الأشغال', 'صيدلية المركز', 'مديرية السكن', 'متوسطة الأمل'
    ],
    'الجنس': [
        'ذكر', 'أنثى', 'ذكر', 'أنثى', 'ذكر',
        'أنثى', 'ذكر', 'أنثى', 'ذكر', 'أنثى'
    ],
    'الحالة_الاجتماعية': [
        'متزوج', 'متزوج', 'عازب', 'متزوج', 'متزوج',
        'متزوج', 'عازب', 'متزوج', 'متزوج', 'عازب'
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(data)

# حفظ الملف
filename = 'نموذج_العمال_للاستيراد.xlsx'
with pd.ExcelWriter(filename, engine='openpyxl') as writer:
    df.to_excel(writer, sheet_name='العمال', index=False)
    
    # تنسيق الجدول
    workbook = writer.book
    worksheet = writer.sheets['العمال']
    
    # تعديل عرض الأعمدة
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column_letter].width = adjusted_width

print(f"تم إنشاء الملف: {filename}")
print(f"عدد العمال: {len(df)}")
print("\nمعاينة البيانات:")
print(df.head())
