#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء شعار تجريبي للاختبار
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_logo():
    """إنشاء شعار تجريبي"""
    # إنشاء صورة جديدة بخلفية شفافة
    width, height = 500, 500
    image = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # رسم دائرة خارجية
    circle_color = (34, 139, 34, 100)  # أخضر شفاف
    draw.ellipse([50, 50, 450, 450], fill=circle_color, outline=(34, 139, 34, 200), width=5)
    
    # رسم دائرة داخلية
    inner_circle_color = (255, 255, 255, 150)  # أبيض شفاف
    draw.ellipse([100, 100, 400, 400], fill=inner_circle_color, outline=(34, 139, 34, 150), width=3)
    
    # رسم نص تجريبي
    try:
        # محاولة استخدام خط عربي
        font_size = 40
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # استخدام الخط الافتراضي إذا لم يتوفر الخط العربي
        font = ImageFont.load_default()
    
    # النص التجريبي
    text1 = "CNOSTE"
    text2 = "Djelfa"
    
    # حساب موقع النص
    bbox1 = draw.textbbox((0, 0), text1, font=font)
    text1_width = bbox1[2] - bbox1[0]
    text1_height = bbox1[3] - bbox1[1]
    
    bbox2 = draw.textbbox((0, 0), text2, font=font)
    text2_width = bbox2[2] - bbox2[0]
    text2_height = bbox2[3] - bbox2[1]
    
    # رسم النص
    text_color = (34, 139, 34, 255)  # أخضر غامق
    draw.text(((width - text1_width) // 2, (height - text1_height) // 2 - 30), 
              text1, fill=text_color, font=font)
    draw.text(((width - text2_width) // 2, (height - text2_height) // 2 + 30), 
              text2, fill=text_color, font=font)
    
    # رسم رموز تجريبية
    # رسم مثلثات صغيرة في الأعلى
    for i in range(5):
        x = 200 + i * 20
        y = 150
        draw.polygon([(x, y), (x+10, y-15), (x+20, y)], fill=(255, 0, 0, 150))
    
    # حفظ الصورة
    logo_path = os.path.join('app', 'static', 'images', 'logo.png')
    os.makedirs(os.path.dirname(logo_path), exist_ok=True)
    image.save(logo_path, 'PNG')
    print(f"تم إنشاء الشعار التجريبي: {logo_path}")

if __name__ == "__main__":
    create_sample_logo()
