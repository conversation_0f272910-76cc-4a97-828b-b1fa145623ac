from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_wtf.csrf import CSRFProtect
from app.models import db
import os

def create_app():
    app = Flask(__name__)
    
    # التكوين
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///workers_committee.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['UPLOAD_FOLDER'] = os.path.join(app.root_path, 'static', 'uploads')
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
    
    # تهيئة قاعدة البيانات
    db.init_app(app)

    # تهيئة CSRF Protection
    csrf = CSRFProtect(app)
    
    # تسجيل المسارات
    from app.routes import main
    app.register_blueprint(main)
    
    # إنشاء مجلد الصور إذا لم يكن موجوداً
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # إنشاء الجداول
    with app.app_context():
        db.create_all()

    return app
