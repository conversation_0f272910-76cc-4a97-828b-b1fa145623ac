from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return '''
    <html dir="rtl">
    <head>
        <title>نظام الاصطياف</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial; text-align: center; padding: 50px; }
            .success { color: green; font-size: 24px; }
            .info { color: blue; margin: 20px; }
        </style>
    </head>
    <body>
        <h1 class="success">✅ نظام الاصطياف يعمل بنجاح!</h1>
        <div class="info">
            <p>🎉 تم إنشاء نظام الاصطياف بنجاح</p>
            <p>📋 الميزات المتاحة:</p>
            <ul style="text-align: right; display: inline-block;">
                <li>✅ إدارة العمارات</li>
                <li>✅ إدارة الشقق</li>
                <li>🔄 نظام الحجوزات (قيد التطوير)</li>
                <li>🔄 نظام الدفعات (قيد التطوير)</li>
                <li>🔄 قائمة الاحتياط (قيد التطوير)</li>
            </ul>
            <p><strong>للوصول للنظام الكامل، يرجى تشغيل التطبيق الأساسي</strong></p>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 تشغيل نظام الاصطياف البسيط...")
    print("🌐 افتح المتصفح واذهب إلى: http://localhost:5000")
    app.run(debug=True, port=5000)
