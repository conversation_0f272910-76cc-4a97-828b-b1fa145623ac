#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف لإضافة بيانات تجريبية للنظام
"""

from app import create_app, db
from app.models import Worker, Child
from datetime import datetime, date

def add_sample_data():
    """إضافة بيانات تجريبية"""
    app = create_app()
    
    with app.app_context():
        # حذف البيانات الموجودة
        Child.query.delete()
        Worker.query.delete()
        db.session.commit()
        
        # إضافة عمال تجريبيين
        workers_data = [
            {
                'first_name': 'أحمد',
                'last_name': 'بن علي',
                'birth_date': date(1985, 3, 15),
                'birth_place': 'الجلفة',
                'gender': 'ذكر',
                'marital_status': 'متزوج',
                'job_title': 'مهندس مدني',
                'position': 'رئيس قسم',
                'workplace': 'مديرية الأشغال العمومية',
                'children': [
                    {
                        'name': 'فاطمة',
                        'birth_date': date(2010, 6, 20),
                        'gender': 'أنثى',
                        'education_level': 'متوسط'
                    },
                    {
                        'name': 'محمد',
                        'birth_date': date(2012, 9, 10),
                        'gender': 'ذكر',
                        'education_level': 'ابتدائي'
                    }
                ]
            },
            {
                'first_name': 'خديجة',
                'last_name': 'بن سالم',
                'birth_date': date(1990, 7, 22),
                'birth_place': 'عين وسارة',
                'gender': 'أنثى',
                'marital_status': 'متزوج',
                'job_title': 'معلمة',
                'position': 'معلمة أولى',
                'workplace': 'ابتدائية الشهيد بوضياف',
                'children': [
                    {
                        'name': 'يوسف',
                        'birth_date': date(2015, 4, 8),
                        'gender': 'ذكر',
                        'education_level': 'ابتدائي'
                    }
                ]
            },
            {
                'first_name': 'عبد الرحمن',
                'last_name': 'قاسمي',
                'birth_date': date(1982, 11, 5),
                'birth_place': 'مسعد',
                'gender': 'ذكر',
                'marital_status': 'عازب',
                'job_title': 'طبيب',
                'position': 'طبيب عام',
                'workplace': 'المستشفى المحلي',
                'children': []
            },
            {
                'first_name': 'زينب',
                'last_name': 'العربي',
                'birth_date': date(1988, 2, 14),
                'birth_place': 'حاسي بحبح',
                'gender': 'أنثى',
                'marital_status': 'متزوج',
                'job_title': 'ممرضة',
                'position': 'ممرضة رئيسية',
                'workplace': 'عيادة متعددة الخدمات',
                'children': [
                    {
                        'name': 'عائشة',
                        'birth_date': date(2008, 12, 3),
                        'gender': 'أنثى',
                        'education_level': 'ثانوي'
                    },
                    {
                        'name': 'عمر',
                        'birth_date': date(2011, 8, 17),
                        'gender': 'ذكر',
                        'education_level': 'متوسط'
                    },
                    {
                        'name': 'سارة',
                        'birth_date': date(2014, 5, 25),
                        'gender': 'أنثى',
                        'education_level': 'ابتدائي'
                    }
                ]
            },
            {
                'first_name': 'مصطفى',
                'last_name': 'بوعلام',
                'birth_date': date(1975, 9, 30),
                'birth_place': 'الجلفة',
                'gender': 'ذكر',
                'marital_status': 'متزوج',
                'job_title': 'مدير إداري',
                'position': 'مدير الموارد البشرية',
                'workplace': 'مديرية التربية',
                'children': [
                    {
                        'name': 'خالد',
                        'birth_date': date(2000, 1, 12),
                        'gender': 'ذكر',
                        'education_level': 'جامعي'
                    },
                    {
                        'name': 'أمينة',
                        'birth_date': date(2003, 7, 8),
                        'gender': 'أنثى',
                        'education_level': 'ثانوي'
                    }
                ]
            }
        ]
        
        # إضافة العمال
        for worker_data in workers_data:
            children_data = worker_data.pop('children')
            
            worker = Worker(**worker_data)
            db.session.add(worker)
            db.session.flush()  # للحصول على ID العامل
            
            # إضافة الأطفال
            for child_data in children_data:
                child_data['worker_id'] = worker.id
                child = Child(**child_data)
                db.session.add(child)
        
        db.session.commit()
        print("تم إضافة البيانات التجريبية بنجاح!")
        print(f"تم إضافة {len(workers_data)} عمال")
        
        # عرض إحصائيات
        total_workers = Worker.query.count()
        total_children = Child.query.count()
        married_workers = Worker.query.filter_by(marital_status='متزوج').count()
        
        print(f"إجمالي العمال: {total_workers}")
        print(f"إجمالي الأطفال: {total_children}")
        print(f"العمال المتزوجون: {married_workers}")

if __name__ == '__main__':
    add_sample_data()
