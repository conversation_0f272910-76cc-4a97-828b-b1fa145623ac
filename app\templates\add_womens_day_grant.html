{% extends "base.html" %}

{% block title %}
إضافة منحة 8 مارس - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-gift text-pink me-2"></i>
                    إضافة منحة 8 مارس - عيد المرأة
                </h5>
            </div>
            <div class="card-body">
                <!-- معلومات مهمة -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i>معلومات مهمة:</h6>
                    <ul class="mb-0">
                        <li>سيتم إنشاء المنحة لجميع العاملات الإناث في النظام</li>
                        <li>عدد العاملات الإناث الحالي: <strong>{{ total_female_workers }} عاملة</strong></li>
                        <li>المبلغ الإجمالي = مبلغ المنحة × عدد العاملات</li>
                        <li>يمكن إنشاء منحة واحدة فقط لكل سنة</li>
                    </ul>
                </div>

                {% if total_female_workers == 0 %}
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i>تنبيه:</h6>
                    <p class="mb-0">لا توجد عاملات إناث في النظام. يجب إضافة عاملات أولاً قبل إنشاء المنحة.</p>
                </div>
                {% endif %}

                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.year.label(class="form-label") }}
                                {{ form.year(class="form-control", id="year") }}
                                {% if form.year.errors %}
                                    <div class="text-danger">
                                        {% for error in form.year.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.grant_amount.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.grant_amount(class="form-control", id="grantAmount", step="0.01", min="0") }}
                                    <span class="input-group-text">دج</span>
                                </div>
                                {% if form.grant_amount.errors %}
                                    <div class="text-danger">
                                        {% for error in form.grant_amount.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.issue_date.label(class="form-label") }}
                                {{ form.issue_date(class="form-control") }}
                                {% if form.issue_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.issue_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="3", placeholder="ملاحظات إضافية حول المنحة...") }}
                                {% if form.notes.errors %}
                                    <div class="text-danger">
                                        {% for error in form.notes.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- معاينة الحسابات -->
                    <div class="card bg-light mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-calculator me-1"></i>معاينة الحسابات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h5 class="text-primary">{{ total_female_workers }}</h5>
                                        <small class="text-muted">عدد المستفيدات</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h5 class="text-success" id="individualAmount">5,000.00 دج</h5>
                                        <small class="text-muted">مبلغ المنحة الفردية</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <h5 class="text-danger" id="totalAmount">{{ "{:,.2f}".format(5000 * total_female_workers) }} دج</h5>
                                        <small class="text-muted">المبلغ الإجمالي</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('main.womens_day_grants_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>إلغاء
                                </a>
                                {{ form.submit(class="btn btn-primary", disabled=total_female_workers == 0) }}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const grantAmountInput = document.getElementById('grantAmount');
    const individualAmountDisplay = document.getElementById('individualAmount');
    const totalAmountDisplay = document.getElementById('totalAmount');
    const totalFemaleWorkers = {{ total_female_workers }};
    
    function updateCalculations() {
        const grantAmount = parseFloat(grantAmountInput.value) || 0;
        const totalAmount = grantAmount * totalFemaleWorkers;
        
        // تنسيق المبلغ الفردي
        individualAmountDisplay.textContent = grantAmount.toLocaleString('ar-DZ', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' دج';
        
        // تنسيق المبلغ الإجمالي
        totalAmountDisplay.textContent = totalAmount.toLocaleString('ar-DZ', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' دج';
    }
    
    // تحديث الحسابات عند تغيير المبلغ
    grantAmountInput.addEventListener('input', updateCalculations);
    
    // تحديث أولي
    updateCalculations();
});
</script>

<style>
.text-pink {
    color: #e91e63 !important;
}

.card-body .card {
    border: 1px solid #dee2e6;
}

.input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
}

#individualAmount, #totalAmount {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.alert ul {
    padding-left: 1.5rem;
}

.alert li {
    margin-bottom: 0.25rem;
}
</style>
{% endblock %}
