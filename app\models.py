from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date

db = SQLAlchemy()

class Worker(db.Model):
    """نموذج العامل"""
    __tablename__ = 'workers'

    id = db.Column(db.Integer, primary_key=True)

    # رقم التسجيل الخاص
    registration_number = db.Column(db.String(20), unique=True, comment='رقم التسجيل الخاص')

    # البيانات الشخصية الأساسية
    first_name = db.Column(db.String(100), nullable=False, comment='الاسم')
    last_name = db.Column(db.String(100), nullable=False, comment='اللقب')
    birth_date = db.Column(db.Date, nullable=False, comment='تاريخ الميلاد')
    birth_place = db.Column(db.String(200), nullable=False, comment='مكان الميلاد')
    personal_address = db.Column(db.String(300), nullable=False, comment='العنوان الشخصي')
    phone = db.Column(db.String(15), nullable=False, comment='الهاتف')
    gender = db.Column(db.String(10), nullable=False, comment='الجنس')  # ذكر/أنثى
    marital_status = db.Column(db.String(20), nullable=False, comment='الحالة الاجتماعية')  # متزوج/عازب/مطلق/أرمل

    # بيانات العمل
    position = db.Column(db.String(200), nullable=False, comment='المنصب')
    workplace = db.Column(db.String(300), nullable=False, comment='مكان العمل')

    # معلومات الهوية والضمان الاجتماعي
    social_security_number = db.Column(db.String(20), comment='رقم الضمان الاجتماعي')
    id_type = db.Column(db.String(50), comment='نوع الهوية')  # بطاقة التعريف الوطنية/جواز السفر/إلخ
    national_id_number = db.Column(db.String(20), comment='رقم بطاقة التعريف الوطنية')
    id_issue_date = db.Column(db.Date, comment='تاريخ صدور الهوية')
    id_issue_place = db.Column(db.String(200), comment='مكان صدور الهوية')

    # معلومات الحساب البنكي/البريدي
    account_number = db.Column(db.String(20), comment='رقم الحساب البنكي/البريدي')
    account_type = db.Column(db.String(10), comment='نوع الحساب')  # بنكي/بريدي
    bank_name = db.Column(db.String(100), comment='اسم البنك')
    bank_agency = db.Column(db.String(100), comment='الوكالة')

    # ملفات ومعلومات إضافية
    photo_filename = db.Column(db.String(255), comment='اسم ملف الصورة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    
    # العلاقة مع الأطفال
    children = db.relationship('Child', backref='worker', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Worker {self.last_name} {self.first_name}>'
    
    @property
    def full_name(self):
        return f"{self.last_name} {self.first_name}"
    
    @property
    def children_count(self):
        return len(self.children)

class Child(db.Model):
    """نموذج الطفل"""
    __tablename__ = 'children'
    
    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('workers.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False, comment='اسم الطفل')
    birth_date = db.Column(db.Date, nullable=False, comment='تاريخ ميلاد الطفل')
    gender = db.Column(db.String(10), nullable=False, comment='جنس الطفل')  # ذكر/أنثى
    education_level = db.Column(db.String(20), nullable=False, comment='المستوى الدراسي')  # ابتدائي/متوسط/ثانوي/جامعي
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    
    def __repr__(self):
        return f'<Child {self.name}>'

class Bank(db.Model):
    """نموذج البنوك"""
    __tablename__ = 'banks'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True, comment='اسم البنك')
    is_active = db.Column(db.Boolean, default=True, comment='نشط')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')

    def __repr__(self):
        return f'<Bank {self.name}>'

class ThermalBathReceipt(db.Model):
    """نموذج وصولات الحمام المعدني"""
    __tablename__ = 'thermal_bath_receipts'

    id = db.Column(db.Integer, primary_key=True)
    receipt_number = db.Column(db.String(20), unique=True, nullable=False, comment='رقم الوصل')
    worker_id = db.Column(db.Integer, db.ForeignKey('workers.id'), nullable=False, comment='معرف العامل')
    issue_date = db.Column(db.Date, nullable=False, comment='تاريخ الإصدار')
    total_amount = db.Column(db.Float, nullable=False, comment='المبلغ الإجمالي')
    committee_contribution = db.Column(db.Float, nullable=False, comment='مساهمة اللجنة')
    worker_payment = db.Column(db.Float, nullable=False, comment='دفع العامل')
    treatment_location = db.Column(db.String(200), nullable=False, comment='مكان العلاج')
    notes = db.Column(db.Text, comment='ملاحظات')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')

    # العلاقة مع العامل
    worker = db.relationship('Worker', backref=db.backref('thermal_receipts', lazy=True))

    def __repr__(self):
        return f'<ThermalBathReceipt {self.receipt_number}>'


class WomensDayGrant(db.Model):
    """نموذج منحة 8 مارس - عيد المرأة"""
    __tablename__ = 'womens_day_grants'

    id = db.Column(db.Integer, primary_key=True)
    year = db.Column(db.Integer, nullable=False, comment='سنة المنحة')
    grant_amount = db.Column(db.Float, nullable=False, comment='المبلغ الثابت لكل عاملة')
    total_female_workers = db.Column(db.Integer, nullable=False, comment='عدد العاملات المستفيدات')
    total_amount = db.Column(db.Float, nullable=False, comment='المبلغ الإجمالي')
    issue_date = db.Column(db.Date, nullable=False, comment='تاريخ صرف المنحة')
    notes = db.Column(db.Text, comment='ملاحظات')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # فهرس فريد للسنة (منحة واحدة فقط لكل سنة)
    __table_args__ = (db.UniqueConstraint('year', name='unique_year_grant'),)

    @property
    def formatted_total_amount(self):
        """تنسيق المبلغ الإجمالي للعرض"""
        return f"{self.total_amount:,.2f} دج"

    @property
    def formatted_grant_amount(self):
        """تنسيق مبلغ المنحة الفردية للعرض"""
        return f"{self.grant_amount:,.2f} دج"

    def __repr__(self):
        return f'<WomensDayGrant {self.year}: {self.total_female_workers} عاملة>'


class WomensDayGrantRecipient(db.Model):
    """نموذج المستفيدات من منحة 8 مارس"""
    __tablename__ = 'womens_day_grant_recipients'

    id = db.Column(db.Integer, primary_key=True)
    grant_id = db.Column(db.Integer, db.ForeignKey('womens_day_grants.id'), nullable=False, comment='معرف المنحة')
    worker_id = db.Column(db.Integer, db.ForeignKey('workers.id'), nullable=False, comment='معرف العاملة')
    amount_received = db.Column(db.Float, nullable=False, comment='المبلغ المستلم')
    received_date = db.Column(db.Date, nullable=False, comment='تاريخ الاستلام')
    signature_status = db.Column(db.Boolean, default=False, comment='حالة التوقيع')
    notes = db.Column(db.Text, comment='ملاحظات خاصة بالمستفيدة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')

    # العلاقات
    grant = db.relationship('WomensDayGrant', backref='recipients')
    worker = db.relationship('Worker', backref='womens_day_grants')

    # فهرس فريد (عاملة واحدة لكل منحة)
    __table_args__ = (db.UniqueConstraint('grant_id', 'worker_id', name='unique_grant_worker'),)

    @property
    def formatted_amount(self):
        """تنسيق المبلغ للعرض"""
        return f"{self.amount_received:,.2f} دج"

    def __repr__(self):
        return f'<WomensDayGrantRecipient {self.worker.full_name}: {self.amount_received} دج>'


# نماذج نظام الاصطياف
class VacationBuilding(db.Model):
    """نموذج العمارات"""
    __tablename__ = 'vacation_buildings'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='اسم العمارة')
    location = db.Column(db.String(200), nullable=False, comment='الموقع')
    description = db.Column(db.Text, comment='وصف العمارة')
    total_floors = db.Column(db.Integer, nullable=False, comment='عدد الطوابق')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # العلاقة مع الشقق
    apartments = db.relationship('VacationApartment', backref='building', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<VacationBuilding {self.name}>'


class VacationApartment(db.Model):
    """نموذج الشقق"""
    __tablename__ = 'vacation_apartments'

    id = db.Column(db.Integer, primary_key=True)
    building_id = db.Column(db.Integer, db.ForeignKey('vacation_buildings.id'), nullable=False, comment='معرف العمارة')
    apartment_number = db.Column(db.String(20), nullable=False, comment='رقم الشقة')
    floor_number = db.Column(db.Integer, nullable=False, comment='رقم الطابق')
    capacity = db.Column(db.Integer, nullable=False, default=4, comment='السعة (عدد الأشخاص)')
    price_per_week = db.Column(db.Float, nullable=False, comment='السعر لكل أسبوع')
    description = db.Column(db.Text, comment='وصف الشقة')
    is_available = db.Column(db.Boolean, default=True, comment='متاحة للحجز')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # العلاقة مع الحجوزات
    bookings = db.relationship('VacationBooking', backref='apartment', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def formatted_price(self):
        """تنسيق السعر"""
        return f"{self.price_per_week:,.2f} دج"

    @property
    def full_name(self):
        """الاسم الكامل للشقة"""
        return f"{self.building.name} - الطابق {self.floor_number} - شقة {self.apartment_number}"

    def __repr__(self):
        return f'<VacationApartment {self.apartment_number}>'


class VacationBooking(db.Model):
    """نموذج حجوزات الاصطياف"""
    __tablename__ = 'vacation_bookings'

    id = db.Column(db.Integer, primary_key=True)
    booking_number = db.Column(db.String(20), unique=True, nullable=False, comment='رقم الحجز')
    worker_id = db.Column(db.Integer, db.ForeignKey('workers.id'), nullable=False, comment='معرف العامل')
    apartment_id = db.Column(db.Integer, db.ForeignKey('vacation_apartments.id'), nullable=False, comment='معرف الشقة')
    start_date = db.Column(db.Date, nullable=False, comment='تاريخ البداية')
    end_date = db.Column(db.Date, nullable=False, comment='تاريخ النهاية')
    duration_days = db.Column(db.Integer, nullable=False, comment='مدة الإقامة بالأيام')
    total_amount = db.Column(db.Float, nullable=False, comment='المبلغ الإجمالي')
    status = db.Column(db.String(20), nullable=False, default='confirmed', comment='حالة الحجز')  # confirmed, cancelled, completed
    booking_date = db.Column(db.Date, nullable=False, comment='تاريخ الحجز')
    notes = db.Column(db.Text, comment='ملاحظات')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # العلاقات
    worker = db.relationship('Worker', backref='vacation_bookings')
    payments = db.relationship('VacationPayment', backref='booking', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def formatted_total_amount(self):
        """تنسيق المبلغ الإجمالي"""
        return f"{self.total_amount:,.2f} دج"

    @property
    def total_paid(self):
        """إجمالي المبلغ المدفوع"""
        return sum(payment.amount for payment in self.payments if payment.status == 'paid')

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.total_paid

    @property
    def is_fully_paid(self):
        """هل تم دفع المبلغ كاملاً"""
        return self.remaining_amount <= 0

    @property
    def status_display(self):
        """عرض حالة الحجز"""
        status_map = {
            'confirmed': 'مؤكد',
            'cancelled': 'ملغي',
            'completed': 'مكتمل'
        }
        return status_map.get(self.status, self.status)

    def __repr__(self):
        return f'<VacationBooking {self.booking_number}>'


class VacationPayment(db.Model):
    """نموذج دفعات الاصطياف"""
    __tablename__ = 'vacation_payments'

    id = db.Column(db.Integer, primary_key=True)
    booking_id = db.Column(db.Integer, db.ForeignKey('vacation_bookings.id'), nullable=False, comment='معرف الحجز')
    payment_number = db.Column(db.String(20), unique=True, nullable=False, comment='رقم الدفعة')
    amount = db.Column(db.Float, nullable=False, comment='مبلغ الدفعة')
    due_date = db.Column(db.Date, nullable=False, comment='تاريخ الاستحقاق')
    payment_date = db.Column(db.Date, comment='تاريخ الدفع الفعلي')
    status = db.Column(db.String(20), nullable=False, default='pending', comment='حالة الدفعة')  # pending, paid, overdue
    payment_method = db.Column(db.String(50), comment='طريقة الدفع')  # نقدي، تحويل، إلخ
    notes = db.Column(db.Text, comment='ملاحظات')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    @property
    def formatted_amount(self):
        """تنسيق المبلغ"""
        return f"{self.amount:,.2f} دج"

    @property
    def status_display(self):
        """عرض حالة الدفعة"""
        status_map = {
            'pending': 'في الانتظار',
            'paid': 'مدفوع',
            'overdue': 'متأخر'
        }
        return status_map.get(self.status, self.status)

    def __repr__(self):
        return f'<VacationPayment {self.payment_number}>'


class VacationWaitingList(db.Model):
    """نموذج قائمة الاحتياط للاصطياف"""
    __tablename__ = 'vacation_waiting_list'

    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('workers.id'), nullable=False, comment='معرف العامل')
    apartment_id = db.Column(db.Integer, db.ForeignKey('vacation_apartments.id'), nullable=False, comment='معرف الشقة المرغوبة')
    preferred_start_date = db.Column(db.Date, nullable=False, comment='تاريخ البداية المفضل')
    preferred_end_date = db.Column(db.Date, nullable=False, comment='تاريخ النهاية المفضل')
    priority = db.Column(db.Integer, default=1, comment='الأولوية')
    status = db.Column(db.String(20), nullable=False, default='waiting', comment='الحالة')  # waiting, notified, expired
    registration_date = db.Column(db.Date, nullable=False, comment='تاريخ التسجيل')
    notification_date = db.Column(db.Date, comment='تاريخ الإشعار')
    notes = db.Column(db.Text, comment='ملاحظات')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # العلاقات
    worker = db.relationship('Worker', backref='vacation_waiting_list')
    apartment = db.relationship('VacationApartment', backref='waiting_list')

    @property
    def status_display(self):
        """عرض الحالة"""
        status_map = {
            'waiting': 'في الانتظار',
            'notified': 'تم الإشعار',
            'expired': 'منتهي الصلاحية'
        }
        return status_map.get(self.status, self.status)

    def __repr__(self):
        return f'<VacationWaitingList {self.worker.full_name}>'


# نماذج نظام المخيمات والقرعة
class VacationCamp(db.Model):
    """نموذج المخيمات"""
    __tablename__ = 'vacation_camps'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='اسم المخيم')
    location = db.Column(db.String(200), nullable=False, comment='موقع المخيم')
    description = db.Column(db.Text, comment='وصف المخيم')
    duration_days = db.Column(db.Integer, nullable=False, default=7, comment='مدة الإقامة بالأيام')
    is_active = db.Column(db.Boolean, default=True, comment='المخيم نشط')
    start_date = db.Column(db.Date, comment='تاريخ بداية الموسم')
    end_date = db.Column(db.Date, comment='تاريخ نهاية الموسم')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # العلاقة مع العمارات
    buildings = db.relationship('VacationCampBuilding', backref='camp', lazy='dynamic', cascade='all, delete-orphan')

    # العلاقة مع القرعات
    lotteries = db.relationship('VacationLottery', backref='camp', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def total_apartments(self):
        """إجمالي الشقق في المخيم"""
        return sum(building.total_apartments for building in self.buildings)

    @property
    def available_apartments(self):
        """الشقق المتاحة في المخيم"""
        return sum(building.available_apartments for building in self.buildings)

    @property
    def occupied_apartments(self):
        """الشقق المشغولة في المخيم"""
        return self.total_apartments - self.available_apartments

    def __repr__(self):
        return f'<VacationCamp {self.name}>'


class VacationCampBuilding(db.Model):
    """نموذج عمارات المخيم"""
    __tablename__ = 'vacation_camp_buildings'

    id = db.Column(db.Integer, primary_key=True)
    camp_id = db.Column(db.Integer, db.ForeignKey('vacation_camps.id'), nullable=False, comment='معرف المخيم')
    name = db.Column(db.String(100), nullable=False, comment='اسم العمارة')
    building_number = db.Column(db.String(20), nullable=False, comment='رقم العمارة')
    total_floors = db.Column(db.Integer, nullable=False, comment='عدد الطوابق')
    apartments_per_floor = db.Column(db.Integer, nullable=False, default=1, comment='عدد الشقق في كل طابق')
    description = db.Column(db.Text, comment='وصف العمارة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # العلاقة مع الشقق
    apartments = db.relationship('VacationCampApartment', backref='building', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def total_apartments(self):
        """إجمالي الشقق في العمارة"""
        return self.total_floors * self.apartments_per_floor

    @property
    def available_apartments(self):
        """الشقق المتاحة في العمارة"""
        return self.apartments.filter_by(is_occupied=False).count()

    @property
    def full_name(self):
        """الاسم الكامل للعمارة"""
        return f"{self.camp.name} - {self.name} ({self.building_number})"

    def __repr__(self):
        return f'<VacationCampBuilding {self.name}>'


class VacationCampApartment(db.Model):
    """نموذج شقق المخيم"""
    __tablename__ = 'vacation_camp_apartments'

    id = db.Column(db.Integer, primary_key=True)
    building_id = db.Column(db.Integer, db.ForeignKey('vacation_camp_buildings.id'), nullable=False, comment='معرف العمارة')
    apartment_number = db.Column(db.String(20), nullable=False, comment='رقم الشقة')
    floor_number = db.Column(db.Integer, nullable=False, comment='رقم الطابق')
    capacity = db.Column(db.Integer, nullable=False, default=4, comment='السعة (عدد الأشخاص)')
    is_occupied = db.Column(db.Boolean, default=False, comment='مشغولة حالياً')
    current_occupant_id = db.Column(db.Integer, db.ForeignKey('workers.id'), comment='الساكن الحالي')
    occupation_start = db.Column(db.Date, comment='تاريخ بداية الإقامة')
    occupation_end = db.Column(db.Date, comment='تاريخ نهاية الإقامة')
    description = db.Column(db.Text, comment='وصف الشقة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # العلاقات
    current_occupant = db.relationship('Worker', backref='current_camp_apartment')
    lottery_assignments = db.relationship('VacationLotteryAssignment', backref='apartment', lazy='dynamic')

    @property
    def full_name(self):
        """الاسم الكامل للشقة"""
        return f"{self.building.full_name} - الطابق {self.floor_number} - شقة {self.apartment_number}"

    @property
    def is_available(self):
        """هل الشقة متاحة"""
        if not self.is_occupied:
            return True

        # التحقق من انتهاء مدة الإقامة
        if self.occupation_end and self.occupation_end < datetime.now().date():
            return True

        return False

    def free_apartment(self):
        """تحرير الشقة"""
        self.is_occupied = False
        self.current_occupant_id = None
        self.occupation_start = None
        self.occupation_end = None

    def occupy_apartment(self, worker_id, start_date, duration_days):
        """حجز الشقة"""
        from datetime import timedelta

        self.is_occupied = True
        self.current_occupant_id = worker_id
        self.occupation_start = start_date
        self.occupation_end = start_date + timedelta(days=duration_days)

    def __repr__(self):
        return f'<VacationCampApartment {self.apartment_number}>'


class VacationLottery(db.Model):
    """نموذج القرعة"""
    __tablename__ = 'vacation_lotteries'

    id = db.Column(db.Integer, primary_key=True)
    camp_id = db.Column(db.Integer, db.ForeignKey('vacation_camps.id'), nullable=False, comment='معرف المخيم')
    name = db.Column(db.String(100), nullable=False, comment='اسم القرعة')
    description = db.Column(db.Text, comment='وصف القرعة')
    lottery_date = db.Column(db.Date, nullable=False, comment='تاريخ القرعة')
    start_date = db.Column(db.Date, nullable=False, comment='تاريخ بداية الإقامة')
    duration_days = db.Column(db.Integer, nullable=False, comment='مدة الإقامة بالأيام')
    status = db.Column(db.String(20), default='pending', comment='حالة القرعة')  # pending, completed, cancelled
    max_participants = db.Column(db.Integer, comment='الحد الأقصى للمشاركين')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')

    # العلاقات
    participants = db.relationship('VacationLotteryParticipant', backref='lottery', lazy='dynamic', cascade='all, delete-orphan')
    assignments = db.relationship('VacationLotteryAssignment', backref='lottery', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def status_display(self):
        """عرض حالة القرعة"""
        status_map = {
            'pending': 'في الانتظار',
            'completed': 'مكتملة',
            'cancelled': 'ملغية'
        }
        return status_map.get(self.status, self.status)

    @property
    def end_date(self):
        """تاريخ نهاية الإقامة"""
        from datetime import timedelta
        return self.start_date + timedelta(days=self.duration_days)

    @property
    def participants_count(self):
        """عدد المشاركين"""
        return self.participants.count()

    @property
    def winners_count(self):
        """عدد الفائزين"""
        return self.assignments.count()

    def __repr__(self):
        return f'<VacationLottery {self.name}>'


class VacationLotteryParticipant(db.Model):
    """نموذج مشاركي القرعة"""
    __tablename__ = 'vacation_lottery_participants'

    id = db.Column(db.Integer, primary_key=True)
    lottery_id = db.Column(db.Integer, db.ForeignKey('vacation_lotteries.id'), nullable=False, comment='معرف القرعة')
    worker_id = db.Column(db.Integer, db.ForeignKey('workers.id'), nullable=False, comment='معرف العامل')
    registration_date = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ التسجيل')
    is_winner = db.Column(db.Boolean, default=False, comment='فائز في القرعة')
    lottery_number = db.Column(db.Integer, comment='رقم القرعة')
    notes = db.Column(db.Text, comment='ملاحظات')

    # العلاقات
    worker = db.relationship('Worker', backref='lottery_participations')

    def __repr__(self):
        return f'<VacationLotteryParticipant {self.worker.full_name}>'


class VacationLotteryAssignment(db.Model):
    """نموذج تخصيص الشقق للفائزين"""
    __tablename__ = 'vacation_lottery_assignments'

    id = db.Column(db.Integer, primary_key=True)
    lottery_id = db.Column(db.Integer, db.ForeignKey('vacation_lotteries.id'), nullable=False, comment='معرف القرعة')
    participant_id = db.Column(db.Integer, db.ForeignKey('vacation_lottery_participants.id'), nullable=False, comment='معرف المشارك')
    apartment_id = db.Column(db.Integer, db.ForeignKey('vacation_camp_apartments.id'), nullable=False, comment='معرف الشقة')
    assignment_date = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ التخصيص')
    start_date = db.Column(db.Date, nullable=False, comment='تاريخ بداية الإقامة')
    end_date = db.Column(db.Date, nullable=False, comment='تاريخ نهاية الإقامة')
    status = db.Column(db.String(20), default='assigned', comment='حالة التخصيص')  # assigned, confirmed, completed, cancelled
    notes = db.Column(db.Text, comment='ملاحظات')

    # العلاقات
    participant = db.relationship('VacationLotteryParticipant', backref='assignment')

    @property
    def status_display(self):
        """عرض حالة التخصيص"""
        status_map = {
            'assigned': 'مخصص',
            'confirmed': 'مؤكد',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(self.status, self.status)

    def __repr__(self):
        return f'<VacationLotteryAssignment {self.participant.worker.full_name}>'


class VacationWorkerHistory(db.Model):
    """نموذج تاريخ استفادة العمال من المخيمات"""
    __tablename__ = 'vacation_worker_history'

    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('workers.id'), nullable=False, comment='معرف العامل')
    camp_id = db.Column(db.Integer, db.ForeignKey('vacation_camps.id'), nullable=False, comment='معرف المخيم')
    apartment_id = db.Column(db.Integer, db.ForeignKey('vacation_camp_apartments.id'), comment='معرف الشقة')
    lottery_id = db.Column(db.Integer, db.ForeignKey('vacation_lotteries.id'), comment='معرف القرعة')
    start_date = db.Column(db.Date, nullable=False, comment='تاريخ بداية الإقامة')
    end_date = db.Column(db.Date, nullable=False, comment='تاريخ نهاية الإقامة')
    duration_days = db.Column(db.Integer, nullable=False, comment='مدة الإقامة بالأيام')
    year = db.Column(db.Integer, nullable=False, comment='سنة الاستفادة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')

    # العلاقات
    worker = db.relationship('Worker', backref='camp_history')
    camp = db.relationship('VacationCamp', backref='worker_history')
    apartment = db.relationship('VacationCampApartment', backref='history_records')
    lottery = db.relationship('VacationLottery', backref='history_records')

    def __repr__(self):
        return f'<VacationWorkerHistory {self.worker.full_name} - {self.year}>'
