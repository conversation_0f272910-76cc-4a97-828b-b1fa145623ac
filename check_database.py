#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل قاعدة البيانات
"""

import sqlite3
import os

def check_database():
    """فحص هيكل قاعدة البيانات"""
    db_path = 'instance/workers_committee.db'
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # عرض هيكل جدول worker
        cursor.execute("PRAGMA table_info(worker)")
        columns = cursor.fetchall()
        
        print("هيكل جدول worker:")
        print("-" * 50)
        for column in columns:
            print(f"العمود: {column[1]}, النوع: {column[2]}, مطلوب: {'نعم' if column[3] else 'لا'}")
        
        print("\n" + "-" * 50)
        
        # عرض عدد السجلات
        cursor.execute("SELECT COUNT(*) FROM worker")
        count = cursor.fetchone()[0]
        print(f"عدد العمال في قاعدة البيانات: {count}")
        
        if count > 0:
            # عرض أول سجل كمثال
            cursor.execute("SELECT * FROM worker LIMIT 1")
            first_record = cursor.fetchone()
            print(f"\nمثال على أول سجل: {first_record}")
            
    except Exception as e:
        print(f"خطأ في فحص قاعدة البيانات: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
