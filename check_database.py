#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل قاعدة البيانات
"""

import sqlite3
import os

def check_database():
    """فحص هيكل قاعدة البيانات"""
    db_path = 'instance/workers_committee.db'
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # عرض جميع الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("الجداول الموجودة في قاعدة البيانات:")
        for table in tables:
            print(f"- {table[0]}")
        print("-" * 50)

        # عرض هيكل جدول workers
        cursor.execute("PRAGMA table_info(workers)")
        columns = cursor.fetchall()

        print("هيكل جدول workers:")
        print("-" * 50)
        for column in columns:
            print(f"العمود: {column[1]}, النوع: {column[2]}, مطلوب: {'نعم' if column[3] else 'لا'}")

        print("\n" + "-" * 50)

        # عرض عدد السجلات
        cursor.execute("SELECT COUNT(*) FROM workers")
        count = cursor.fetchone()[0]
        print(f"عدد العمال في قاعدة البيانات: {count}")

        # عرض هيكل جدول thermal_bath_receipts
        cursor.execute("PRAGMA table_info(thermal_bath_receipts)")
        receipt_columns = cursor.fetchall()

        print("\nهيكل جدول thermal_bath_receipts:")
        print("-" * 50)
        for column in receipt_columns:
            print(f"العمود: {column[1]}, النوع: {column[2]}, مطلوب: {'نعم' if column[3] else 'لا'}")

        # عرض عدد الوصولات
        cursor.execute("SELECT COUNT(*) FROM thermal_bath_receipts")
        receipt_count = cursor.fetchone()[0]
        print(f"عدد وصولات الحمام المعدني: {receipt_count}")

        # فحص الربط بين الجداول
        if receipt_count > 0:
            cursor.execute("""
                SELECT r.receipt_number, r.worker_id, w.first_name, w.last_name
                FROM thermal_bath_receipts r
                LEFT JOIN workers w ON r.worker_id = w.id
                LIMIT 5
            """)
            receipts = cursor.fetchall()
            print("\nعينة من الوصولات مع معلومات العمال:")
            print("-" * 50)
            for receipt in receipts:
                worker_name = f"{receipt[3]} {receipt[2]}" if receipt[2] and receipt[3] else "غير مرتبط"
                print(f"وصل رقم: {receipt[0]}, معرف العامل: {receipt[1]}, اسم العامل: {worker_name}")

    except Exception as e:
        print(f"خطأ في فحص قاعدة البيانات: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
