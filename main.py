#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ملف التشغيل الرئيسي لتطبيق لجنة الخدمات الاجتماعية
"""

import os
import sys
import webbrowser
import threading
import time

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from app import create_app, db
except ImportError as e:
    print(f"❌ خطأ في استيراد التطبيق: {e}")
    print("💡 تأكد من وجود مجلد app والملفات المطلوبة")
    input("اضغط Enter للخروج...")
    sys.exit(1)

def get_resource_path(relative_path):
    """الحصول على مسار الملف سواء في وضع التطوير أو التنفيذي"""
    try:
        # Py<PERSON>nstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(2)  # انتظار حتى يبدأ الخادم
    webbrowser.open('http://127.0.0.1:5000')

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏢 تطبيق لجنة الخدمات الاجتماعية للعمال")
    print("🏖️ نظام الاصطياف الجديد")
    print("=" * 60)
    print("🚀 جاري تشغيل التطبيق...")

    try:
        # إنشاء التطبيق
        app = create_app()
        print("✅ تم إنشاء التطبيق بنجاح")

        # إنشاء الجداول
        with app.app_context():
            db.create_all()
            print("✅ تم إنشاء جداول قاعدة البيانات")

        # تحديد المنفذ
        port = 5000

        print(f"✅ تم تشغيل التطبيق بنجاح!")
        print(f"🌐 رابط التطبيق: http://127.0.0.1:{port}")
        print(f"📱 سيتم فتح المتصفح تلقائياً...")
        print("=" * 60)
        print("💡 تعليمات:")
        print("   - سيتم فتح التطبيق في المتصفح تلقائياً")
        print("   - لإيقاف التطبيق اضغط Ctrl+C")
        print("   - لا تغلق هذه النافذة أثناء استخدام التطبيق")
        print("=" * 60)
        print("🏖️ الميزات الجديدة:")
        print("   - إدارة العمارات والشقق")
        print("   - نظام الحجوزات (قريباً)")
        print("   - نظام الدفعات (قريباً)")
        print("   - قائمة الاحتياط (قريباً)")
        print("=" * 60)

        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()

        # تشغيل التطبيق
        app.run(
            host='127.0.0.1',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )

    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")
    finally:
        print("👋 شكراً لاستخدام التطبيق!")

if __name__ == '__main__':
    main()
