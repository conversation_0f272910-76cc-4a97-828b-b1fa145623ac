#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("🚀 تشغيل نظام إدارة لجنة الخدمات الاجتماعية...")
print("📁 المجلد الحالي:", current_dir)

try:
    # محاولة استيراد Flask
    from flask import Flask
    print("✅ Flask متوفر")
    
    # محاولة استيراد التطبيق
    from app import create_app, db
    print("✅ تم استيراد التطبيق")
    
    # إنشاء التطبيق
    app = create_app()
    print("✅ تم إنشاء التطبيق")
    
    # إنشاء الجداول
    with app.app_context():
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
    
    print("\n" + "="*50)
    print("🌐 التطبيق متاح على:")
    print("   http://localhost:5000")
    print("   http://127.0.0.1:5000")
    print("="*50)
    print("⚠️  للإيقاف اضغط Ctrl+C")
    print("="*50)
    
    # تشغيل التطبيق
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من وجود مجلد app والملفات المطلوبة")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

print("\n👋 تم إيقاف التطبيق")
input("اضغط Enter للخروج...")
