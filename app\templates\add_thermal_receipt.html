{% extends "base.html" %}

{% block title %}إصدار وصل حمام معدني - لجنة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>إصدار وصل حمام معدني جديد
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" id="receiptForm">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- العامل -->
                            <div class="col-md-6 mb-3">
                                {{ form.worker_id.label(class="form-label required") }}
                                {{ form.worker_id(class="form-select", onchange="showWorkerInfo()") }}
                                {% if form.worker_id.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.worker_id.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معلومات العامل المحدد -->
                        <div id="workerInfo" class="mb-3"></div>

                        <div class="row">

                            <!-- تاريخ الإصدار -->
                            <div class="col-md-6 mb-3">
                                {{ form.issue_date.label(class="form-label required") }}
                                {{ form.issue_date(class="form-control") }}
                                {% if form.issue_date.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.issue_date.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- المبلغ الإجمالي -->
                            <div class="col-md-6 mb-3">
                                {{ form.total_amount.label(class="form-label required") }}
                                <div class="input-group">
                                    {{ form.total_amount(class="form-control", id="totalAmount", oninput="calculateWorkerPayment()") }}
                                    <span class="input-group-text">د.ج</span>
                                </div>
                                {% if form.total_amount.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.total_amount.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- مساهمة اللجنة -->
                            <div class="col-md-6 mb-3">
                                {{ form.committee_contribution.label(class="form-label required") }}
                                <div class="input-group">
                                    {{ form.committee_contribution(class="form-control", id="committeeContribution", oninput="calculateWorkerPayment()") }}
                                    <span class="input-group-text">د.ج</span>
                                </div>
                                {% if form.committee_contribution.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.committee_contribution.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- دفع العامل (محسوب تلقائياً) -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">دفع العامل</label>
                                <div class="input-group">
                                    <input type="text" class="form-control bg-light" id="workerPayment" readonly>
                                    <span class="input-group-text">د.ج</span>
                                </div>
                                <small class="text-muted">يتم حساب هذا المبلغ تلقائياً (المبلغ الإجمالي - مساهمة اللجنة)</small>
                            </div>
                        </div>

                        <!-- مكان العلاج -->
                        <div class="mb-3">
                            {{ form.treatment_location.label(class="form-label required") }}
                            {{ form.treatment_location(class="form-control") }}
                            {% if form.treatment_location.errors %}
                                <div class="text-danger small">
                                    {% for error in form.treatment_location.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- ملاحظات -->
                        <div class="mb-4">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger small">
                                    {% for error in form.notes.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.thermal_receipts_list') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function calculateWorkerPayment() {
    const totalAmount = parseFloat(document.getElementById('totalAmount').value) || 0;
    const committeeContribution = parseFloat(document.getElementById('committeeContribution').value) || 0;
    const workerPayment = totalAmount - committeeContribution;

    document.getElementById('workerPayment').value = workerPayment >= 0 ? workerPayment.toFixed(2) : '0.00';

    // تغيير لون الحقل حسب القيمة
    const workerPaymentField = document.getElementById('workerPayment');
    if (workerPayment < 0) {
        workerPaymentField.classList.add('is-invalid');
        workerPaymentField.classList.remove('is-valid');
    } else {
        workerPaymentField.classList.remove('is-invalid');
        workerPaymentField.classList.add('is-valid');
    }
}

// عرض معلومات العامل المحدد
function showWorkerInfo() {
    const workerSelect = document.getElementById('worker_id');
    const selectedOption = workerSelect.options[workerSelect.selectedIndex];
    const workerInfoDiv = document.getElementById('workerInfo');

    if (workerSelect.value && workerSelect.value !== '0') {
        const workerText = selectedOption.text;
        const parts = workerText.split(' - ');
        if (parts.length >= 3) {
            workerInfoDiv.innerHTML = `
                <div class="alert alert-info">
                    <strong>العامل المحدد:</strong> ${parts[0]}<br>
                    <strong>المنصب:</strong> ${parts[1]}<br>
                    <strong>مكان العمل:</strong> ${parts[2]}
                </div>
            `;
        }
    } else {
        workerInfoDiv.innerHTML = '';
    }
}

// حساب دفع العامل وعرض معلومات العامل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateWorkerPayment();
    showWorkerInfo();
});

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('receiptForm').addEventListener('submit', function(e) {
    const totalAmount = parseFloat(document.getElementById('totalAmount').value) || 0;
    const committeeContribution = parseFloat(document.getElementById('committeeContribution').value) || 0;
    
    if (committeeContribution > totalAmount) {
        e.preventDefault();
        alert('مساهمة اللجنة لا يمكن أن تكون أكبر من المبلغ الإجمالي');
        return false;
    }
    
    if (totalAmount <= 0) {
        e.preventDefault();
        alert('يجب أن يكون المبلغ الإجمالي أكبر من صفر');
        return false;
    }
});
</script>

<style>
.required::after {
    content: " *";
    color: red;
}

.is-invalid {
    border-color: #dc3545;
}

.is-valid {
    border-color: #198754;
}
</style>
{% endblock %}
