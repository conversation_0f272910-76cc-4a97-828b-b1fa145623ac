{% extends "base.html" %}

{% block title %}
تفاصيل منحة 8 مارس {{ grant.year }} - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- معلومات المنحة -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-gift text-pink me-2"></i>
                    منحة 8 مارس - عيد المرأة {{ grant.year }}
                </h5>
                <div class="btn-group">
                    <a href="{{ url_for('main.edit_womens_day_grant', id=grant.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </a>
                    <a href="{{ url_for('main.print_womens_day_grant', id=grant.id) }}" class="btn btn-success" target="_blank">
                        <i class="fas fa-print me-1"></i>طباعة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <h4>{{ grant.year }}</h4>
                                <p class="mb-0">سنة المنحة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-money-bill fa-2x mb-2"></i>
                                <h4>{{ grant.formatted_grant_amount }}</h4>
                                <p class="mb-0">مبلغ المنحة الفردية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-female fa-2x mb-2"></i>
                                <h4>{{ grant.total_female_workers }}</h4>
                                <p class="mb-0">عدد المستفيدات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white text-center">
                            <div class="card-body">
                                <i class="fas fa-calculator fa-2x mb-2"></i>
                                <h4>{{ grant.formatted_total_amount }}</h4>
                                <p class="mb-0">المبلغ الإجمالي</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <p><strong>تاريخ الصرف:</strong> {{ grant.issue_date.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>تاريخ الإنشاء:</strong> {{ grant.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
                
                {% if grant.notes %}
                <div class="row">
                    <div class="col-12">
                        <p><strong>ملاحظات:</strong></p>
                        <div class="alert alert-light">{{ grant.notes }}</div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- قائمة المستفيدات -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list me-1"></i>
                    قائمة المستفيدات ({{ recipients|length }})
                    
                    <!-- إحصائيات التوقيعات -->
                    {% set signed_count = recipients|selectattr('signature_status', 'equalto', true)|list|length %}
                    <span class="ms-3">
                        {% if signed_count == recipients|length %}
                            <span class="badge bg-success">جميع التوقيعات مكتملة</span>
                        {% elif signed_count > 0 %}
                            <span class="badge bg-warning">{{ signed_count }} من {{ recipients|length }} وقعت</span>
                        {% else %}
                            <span class="badge bg-danger">لم تبدأ التوقيعات</span>
                        {% endif %}
                    </span>
                </h6>
            </div>
            <div class="card-body">
                {% if recipients %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>#</th>
                                <th>رقم التسجيل</th>
                                <th>اسم العاملة</th>
                                <th>المنصب</th>
                                <th>مكان العمل</th>
                                <th>المبلغ المستحق</th>
                                <th>تاريخ الاستلام</th>
                                <th>حالة التوقيع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for recipient in recipients %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <span class="badge bg-dark">{{ recipient.worker.registration_number or 'غير محدد' }}</span>
                                </td>
                                <td>
                                    <a href="{{ url_for('main.worker_detail', id=recipient.worker.id) }}" 
                                       class="text-decoration-none">
                                        {{ recipient.worker.full_name }}
                                    </a>
                                </td>
                                <td>{{ recipient.worker.position }}</td>
                                <td>{{ recipient.worker.workplace }}</td>
                                <td>
                                    <strong class="text-success">{{ recipient.formatted_amount }}</strong>
                                </td>
                                <td>{{ recipient.received_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if recipient.signature_status %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>تم التوقيع
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>لم يتم التوقيع
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('main.toggle_signature_status', grant_id=grant.id, recipient_id=recipient.id) }}" 
                                       class="btn btn-sm {% if recipient.signature_status %}btn-outline-warning{% else %}btn-outline-success{% endif %}"
                                       title="{% if recipient.signature_status %}إلغاء التوقيع{% else %}تأكيد التوقيع{% endif %}">
                                        {% if recipient.signature_status %}
                                            <i class="fas fa-undo"></i>
                                        {% else %}
                                            <i class="fas fa-check"></i>
                                        {% endif %}
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- ملخص المبالغ -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="text-success">{{ recipients|length }} × {{ grant.formatted_grant_amount }}</h5>
                                <p class="mb-0">عدد المستفيدات × المبلغ الفردي</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="text-primary">{{ signed_count }} / {{ recipients|length }}</h5>
                                <p class="mb-0">التوقيعات المكتملة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="text-danger">{{ grant.formatted_total_amount }}</h5>
                                <p class="mb-0">المبلغ الإجمالي</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5 class="text-muted">لا توجد مستفيدات</h5>
                    <p class="text-muted">حدث خطأ في إنشاء قائمة المستفيدات</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- أزرار التنقل -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('main.womens_day_grants_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                    <div>
                        <a href="{{ url_for('main.edit_womens_day_grant', id=grant.id) }}" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i>تعديل المنحة
                        </a>
                        <a href="{{ url_for('main.print_womens_day_grant', id=grant.id) }}" class="btn btn-success" target="_blank">
                            <i class="fas fa-print me-1"></i>طباعة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.text-pink {
    color: #e91e63 !important;
}

.card-body .card {
    transition: transform 0.2s;
    margin-bottom: 1rem;
}

.card-body .card:hover {
    transform: translateY(-2px);
}

.table th {
    border-top: none;
}

.badge {
    font-size: 0.8em;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}
