{% extends "base.html" %}

{% block title %}إدارة الشقق - نظام الاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-home"></i>
                        إدارة الشقق
                    </h3>
                    <div>
                        <a href="{{ url_for('main.add_vacation_apartment') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            إضافة شقة جديدة
                        </a>
                        <a href="{{ url_for('main.vacation_buildings_list') }}" class="btn btn-secondary">
                            <i class="fas fa-building"></i>
                            إدارة العمارات
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if apartments %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="20%">العمارة</th>
                                        <th width="10%">الطابق</th>
                                        <th width="10%">رقم الشقة</th>
                                        <th width="10%">السعة</th>
                                        <th width="15%">السعر/أسبوع</th>
                                        <th width="10%">الحالة</th>
                                        <th width="20%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for apartment in apartments %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <strong>{{ apartment.building.name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ apartment.building.location }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">الطابق {{ apartment.floor_number }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ apartment.apartment_number }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ apartment.capacity }} أشخاص</span>
                                        </td>
                                        <td>
                                            <strong class="text-success">{{ apartment.formatted_price }}</strong>
                                        </td>
                                        <td>
                                            {% if apartment.is_available %}
                                                <span class="badge bg-success">متاحة</span>
                                            {% else %}
                                                <span class="badge bg-danger">غير متاحة</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="#" class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-success" title="الحجوزات">
                                                    <i class="fas fa-calendar-check"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-danger" title="حذف"
                                                   onclick="return confirm('هل أنت متأكد من حذف هذه الشقة؟')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-home fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد شقق مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة شقة جديدة لنظام الاصطياف</p>
                            <a href="{{ url_for('main.add_vacation_apartment') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                إضافة شقة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if apartments %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ apartments|length }}</h4>
                        <p class="mb-0">إجمالي الشقق</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-home fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ apartments|selectattr('is_available')|list|length }}</h4>
                        <p class="mb-0">شقق متاحة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ apartments|sum(attribute='capacity') }}</h4>
                        <p class="mb-0">إجمالي السعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>0</h4>
                        <p class="mb-0">حجوزات نشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- فلترة الشقق -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-filter"></i>
                    فلترة الشقق
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <select class="form-select" id="buildingFilter">
                            <option value="">جميع العمارات</option>
                            {% for apartment in apartments %}
                                {% if apartment.building.name not in (apartments|map(attribute='building.name')|list)[:loop.index-1] %}
                                    <option value="{{ apartment.building.name }}">{{ apartment.building.name }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="available">متاحة</option>
                            <option value="unavailable">غير متاحة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="number" class="form-control" id="capacityFilter" placeholder="السعة الأدنى">
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function clearFilters() {
    document.getElementById('buildingFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('capacityFilter').value = '';
    // إعادة عرض جميع الصفوف
    $('tbody tr').show();
}

$(document).ready(function() {
    // فلترة الجدول
    $('#buildingFilter, #statusFilter, #capacityFilter').on('change keyup', function() {
        var buildingFilter = $('#buildingFilter').val().toLowerCase();
        var statusFilter = $('#statusFilter').val();
        var capacityFilter = parseInt($('#capacityFilter').val()) || 0;
        
        $('tbody tr').each(function() {
            var building = $(this).find('td:eq(1)').text().toLowerCase();
            var status = $(this).find('td:eq(6) .badge').hasClass('bg-success') ? 'available' : 'unavailable';
            var capacity = parseInt($(this).find('td:eq(4)').text()) || 0;
            
            var showRow = true;
            
            if (buildingFilter && !building.includes(buildingFilter)) {
                showRow = false;
            }
            
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }
            
            if (capacityFilter && capacity < capacityFilter) {
                showRow = false;
            }
            
            if (showRow) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
});
</script>
{% endblock %}
