{% extends "base.html" %}

{% block title %}إضافة دفعة - {{ booking.booking_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus text-success"></i>
                        إضافة دفعة - {{ booking.booking_number }}
                    </h3>
                </div>

                <!-- معلومات الحجز -->
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> معلومات الحجز:</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>العامل:</strong> {{ booking.worker.full_name }}</p>
                                <p><strong>الشقة:</strong> {{ booking.apartment.full_name }}</p>
                                <p><strong>المدة:</strong> {{ booking.duration_days }} يوم</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>المبلغ الإجمالي:</strong> {{ booking.formatted_total_amount }}</p>
                                <p><strong>المبلغ المدفوع:</strong> {{ "{:,.2f}".format(booking.total_paid) }} دج</p>
                                <p><strong>المبلغ المتبقي:</strong> <span class="text-warning">{{ "{:,.2f}".format(booking.remaining_amount) }} دج</span></p>
                            </div>
                        </div>
                    </div>

                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.amount.label(class="form-label") }}
                                    {{ form.amount(class="form-control", step="0.01", max=booking.remaining_amount) }}
                                    <small class="form-text text-muted">الحد الأقصى: {{ "{:,.2f}".format(booking.remaining_amount) }} دج</small>
                                    {% if form.amount.errors %}
                                        <div class="text-danger">
                                            {% for error in form.amount.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.payment_date.label(class="form-label") }}
                                    {{ form.payment_date(class="form-control", type="date") }}
                                    {% if form.payment_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.payment_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            {{ form.payment_method.label(class="form-label") }}
                            {{ form.payment_method(class="form-control") }}
                            {% if form.payment_method.errors %}
                                <div class="text-danger">
                                    {% for error in form.payment_method.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="3") }}
                            {% if form.notes.errors %}
                                <div class="text-danger">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.submit(class="btn btn-success btn-block") }}
                                </div>
                                <div class="col-md-6">
                                    <a href="{{ url_for('main.vacation_booking_detail', id=booking.id) }}" class="btn btn-secondary btn-block">
                                        <i class="fas fa-arrow-left"></i> العودة للحجز
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // تعيين التاريخ الحالي كافتراضي
    var today = new Date().toISOString().split('T')[0];
    $('#payment_date').val(today);

    // تحديث المبلغ المتبقي عند تغيير المبلغ
    $('#amount').on('input', function() {
        var amount = parseFloat($(this).val()) || 0;
        var remaining = {{ booking.remaining_amount }};
        
        if (amount > remaining) {
            $(this).val(remaining.toFixed(2));
            alert('المبلغ لا يمكن أن يكون أكبر من المبلغ المتبقي');
        }
    });
});
</script>
{% endblock %}
