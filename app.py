from app import create_app, db
from app.models import Worker, Child, Bank, ThermalBathReceipt, WomensDayGrant, WomensDayGrantRecipient
from app.models import VacationBuilding, VacationApartment, VacationBooking, VacationPayment, VacationWaitingList

app = create_app()

@app.shell_context_processor
def make_shell_context():
    return {
        'db': db,
        'Worker': Worker,
        'Child': Child,
        'Bank': Bank,
        'ThermalBathReceipt': ThermalBathReceipt,
        'WomensDayGrant': WomensDayGrant,
        'WomensDayGrantRecipient': WomensDayGrantRecipient,
        'VacationBuilding': VacationBuilding,
        'VacationApartment': VacationApartment,
        'VacationBooking': VacationBooking,
        'VacationPayment': VacationPayment,
        'VacationWaitingList': VacationWaitingList
    }

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        print("Database tables created successfully!")
    app.run(debug=True)