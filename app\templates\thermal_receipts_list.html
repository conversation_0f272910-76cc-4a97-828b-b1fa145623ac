{% extends "base.html" %}

{% block title %}وصولات الحمام المعدني - لجنة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-receipt me-2"></i>وصولات الحمام المعدني</h2>
                <a href="{{ url_for('main.add_thermal_receipt') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إصدار وصل جديد
                </a>
            </div>

            <!-- شريط البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-8">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="البحث برقم الوصل، اسم العامل، أو مكان العلاج..." 
                                   value="{{ search }}">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            {% if search %}
                            <a href="{{ url_for('main.thermal_receipts_list') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الوصولات -->
            <div class="card">
                <div class="card-body">
                    {% if receipts.items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الوصل</th>
                                    <th>العامل</th>
                                    <th>تاريخ الإصدار</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>مساهمة اللجنة</th>
                                    <th>دفع العامل</th>
                                    <th>مكان العلاج</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for receipt in receipts.items %}
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ receipt.receipt_number }}</strong>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('main.worker_detail', id=receipt.worker.id) }}" 
                                           class="text-decoration-none">
                                            {{ receipt.worker.full_name }}
                                        </a>
                                    </td>
                                    <td>{{ receipt.issue_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ "{:,.0f}".format(receipt.total_amount) }} د.ج</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ "{:,.0f}".format(receipt.committee_contribution) }} د.ج</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">{{ "{:,.0f}".format(receipt.worker_payment) }} د.ج</span>
                                    </td>
                                    <td>{{ receipt.treatment_location }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('main.thermal_receipt_detail', id=receipt.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('main.print_thermal_receipt', id=receipt.id) }}" 
                                               class="btn btn-sm btn-outline-success" title="طباعة" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <a href="{{ url_for('main.edit_thermal_receipt', id=receipt.id) }}" 
                                               class="btn btn-sm btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    title="حذف" onclick="confirmDelete({{ receipt.id }}, '{{ receipt.receipt_number }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if receipts.pages > 1 %}
                    <nav aria-label="صفحات الوصولات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if receipts.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.thermal_receipts_list', page=receipts.prev_num, search=search) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in receipts.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != receipts.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.thermal_receipts_list', page=page_num, search=search) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if receipts.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.thermal_receipts_list', page=receipts.next_num, search=search) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد وصولات</h5>
                        {% if search %}
                        <p class="text-muted">لم يتم العثور على نتائج للبحث "{{ search }}"</p>
                        <a href="{{ url_for('main.thermal_receipts_list') }}" class="btn btn-outline-primary">عرض جميع الوصولات</a>
                        {% else %}
                        <p class="text-muted">لم يتم إصدار أي وصولات بعد</p>
                        <a href="{{ url_for('main.add_thermal_receipt') }}" class="btn btn-primary">إصدار أول وصل</a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الوصل رقم <strong id="receiptNumber"></strong>؟</p>
                <p class="text-danger"><small>هذا الإجراء لا يمكن التراجع عنه.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(receiptId, receiptNumber) {
    document.getElementById('receiptNumber').textContent = receiptNumber;
    document.getElementById('deleteForm').action = '/delete_thermal_receipt/' + receiptId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
