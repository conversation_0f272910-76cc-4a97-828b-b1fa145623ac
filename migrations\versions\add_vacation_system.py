"""Add vacation system tables

Revision ID: vacation_system_001
Revises: 
Create Date: 2025-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = 'vacation_system_001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Create vacation_buildings table
    op.create_table('vacation_buildings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False, comment='اسم العمارة'),
        sa.Column('location', sa.String(length=200), nullable=False, comment='الموقع'),
        sa.Column('description', sa.Text(), nullable=True, comment='وصف العمارة'),
        sa.Column('total_floors', sa.Integer(), nullable=False, comment='عدد الطوابق'),
        sa.Column('created_at', sa.DateTime(), nullable=True, comment='تاريخ الإنشاء'),
        sa.Column('updated_at', sa.DateTime(), nullable=True, comment='تاريخ التحديث'),
        sa.PrimaryKeyConstraint('id')
    )

    # Create vacation_apartments table
    op.create_table('vacation_apartments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('building_id', sa.Integer(), nullable=False, comment='معرف العمارة'),
        sa.Column('apartment_number', sa.String(length=20), nullable=False, comment='رقم الشقة'),
        sa.Column('floor_number', sa.Integer(), nullable=False, comment='رقم الطابق'),
        sa.Column('capacity', sa.Integer(), nullable=False, comment='السعة (عدد الأشخاص)'),
        sa.Column('price_per_week', sa.Float(), nullable=False, comment='السعر لكل أسبوع'),
        sa.Column('description', sa.Text(), nullable=True, comment='وصف الشقة'),
        sa.Column('is_available', sa.Boolean(), nullable=True, comment='متاحة للحجز'),
        sa.Column('created_at', sa.DateTime(), nullable=True, comment='تاريخ الإنشاء'),
        sa.Column('updated_at', sa.DateTime(), nullable=True, comment='تاريخ التحديث'),
        sa.ForeignKeyConstraint(['building_id'], ['vacation_buildings.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create vacation_bookings table
    op.create_table('vacation_bookings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('booking_number', sa.String(length=20), nullable=False, comment='رقم الحجز'),
        sa.Column('worker_id', sa.Integer(), nullable=False, comment='معرف العامل'),
        sa.Column('apartment_id', sa.Integer(), nullable=False, comment='معرف الشقة'),
        sa.Column('start_date', sa.Date(), nullable=False, comment='تاريخ البداية'),
        sa.Column('end_date', sa.Date(), nullable=False, comment='تاريخ النهاية'),
        sa.Column('duration_days', sa.Integer(), nullable=False, comment='مدة الإقامة بالأيام'),
        sa.Column('total_amount', sa.Float(), nullable=False, comment='المبلغ الإجمالي'),
        sa.Column('status', sa.String(length=20), nullable=False, comment='حالة الحجز'),
        sa.Column('booking_date', sa.Date(), nullable=False, comment='تاريخ الحجز'),
        sa.Column('notes', sa.Text(), nullable=True, comment='ملاحظات'),
        sa.Column('created_at', sa.DateTime(), nullable=True, comment='تاريخ الإنشاء'),
        sa.Column('updated_at', sa.DateTime(), nullable=True, comment='تاريخ التحديث'),
        sa.ForeignKeyConstraint(['apartment_id'], ['vacation_apartments.id'], ),
        sa.ForeignKeyConstraint(['worker_id'], ['workers.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('booking_number')
    )

    # Create vacation_payments table
    op.create_table('vacation_payments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('booking_id', sa.Integer(), nullable=False, comment='معرف الحجز'),
        sa.Column('payment_number', sa.String(length=20), nullable=False, comment='رقم الدفعة'),
        sa.Column('amount', sa.Float(), nullable=False, comment='مبلغ الدفعة'),
        sa.Column('due_date', sa.Date(), nullable=False, comment='تاريخ الاستحقاق'),
        sa.Column('payment_date', sa.Date(), nullable=True, comment='تاريخ الدفع الفعلي'),
        sa.Column('status', sa.String(length=20), nullable=False, comment='حالة الدفعة'),
        sa.Column('payment_method', sa.String(length=50), nullable=True, comment='طريقة الدفع'),
        sa.Column('notes', sa.Text(), nullable=True, comment='ملاحظات'),
        sa.Column('created_at', sa.DateTime(), nullable=True, comment='تاريخ الإنشاء'),
        sa.Column('updated_at', sa.DateTime(), nullable=True, comment='تاريخ التحديث'),
        sa.ForeignKeyConstraint(['booking_id'], ['vacation_bookings.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('payment_number')
    )

    # Create vacation_waiting_list table
    op.create_table('vacation_waiting_list',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('worker_id', sa.Integer(), nullable=False, comment='معرف العامل'),
        sa.Column('apartment_id', sa.Integer(), nullable=False, comment='معرف الشقة المرغوبة'),
        sa.Column('preferred_start_date', sa.Date(), nullable=False, comment='تاريخ البداية المفضل'),
        sa.Column('preferred_end_date', sa.Date(), nullable=False, comment='تاريخ النهاية المفضل'),
        sa.Column('priority', sa.Integer(), nullable=True, comment='الأولوية'),
        sa.Column('status', sa.String(length=20), nullable=False, comment='الحالة'),
        sa.Column('registration_date', sa.Date(), nullable=False, comment='تاريخ التسجيل'),
        sa.Column('notification_date', sa.Date(), nullable=True, comment='تاريخ الإشعار'),
        sa.Column('notes', sa.Text(), nullable=True, comment='ملاحظات'),
        sa.Column('created_at', sa.DateTime(), nullable=True, comment='تاريخ الإنشاء'),
        sa.Column('updated_at', sa.DateTime(), nullable=True, comment='تاريخ التحديث'),
        sa.ForeignKeyConstraint(['apartment_id'], ['vacation_apartments.id'], ),
        sa.ForeignKeyConstraint(['worker_id'], ['workers.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    op.drop_table('vacation_waiting_list')
    op.drop_table('vacation_payments')
    op.drop_table('vacation_bookings')
    op.drop_table('vacation_apartments')
    op.drop_table('vacation_buildings')
