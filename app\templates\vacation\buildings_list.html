{% extends "base.html" %}

{% block title %}إدارة العمارات - نظام الاصطياف{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-building"></i>
                        إدارة العمارات
                    </h3>
                    <a href="{{ url_for('main.add_vacation_building') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة عمارة جديدة
                    </a>
                </div>
                
                <div class="card-body">
                    {% if buildings %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="20%">اسم العمارة</th>
                                        <th width="25%">الموقع</th>
                                        <th width="15%">عدد الطوابق</th>
                                        <th width="15%">عدد الشقق</th>
                                        <th width="20%">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for building in buildings %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <strong>{{ building.name }}</strong>
                                        </td>
                                        <td>{{ building.location }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ building.total_floors }} طابق</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ building.apartments.count() }} شقة</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('main.vacation_apartments_list') }}?building_id={{ building.id }}" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض الشقق">
                                                    <i class="fas fa-home"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-danger" title="حذف"
                                                   onclick="return confirm('هل أنت متأكد من حذف هذه العمارة؟')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">لا توجد عمارات مسجلة</h4>
                            <p class="text-muted">ابدأ بإضافة عمارة جديدة لنظام الاصطياف</p>
                            <a href="{{ url_for('main.add_vacation_building') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                إضافة عمارة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if buildings %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ buildings|length }}</h4>
                        <p class="mb-0">إجمالي العمارات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ buildings|sum(attribute='apartments.count') }}</h4>
                        <p class="mb-0">إجمالي الشقق</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-home fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ buildings|sum(attribute='total_floors') }}</h4>
                        <p class="mb-0">إجمالي الطوابق</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-layer-group fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>0</h4>
                        <p class="mb-0">الحجوزات النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
