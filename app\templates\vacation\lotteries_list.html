{% extends "base.html" %}

{% block title %}قائمة القرعات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-dice text-warning"></i>
                        قائمة القرعات
                    </h3>
                    <a href="{{ url_for('main.add_vacation_lottery') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إنشاء قرعة جديدة
                    </a>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-dice"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي القرعات</span>
                                    <span class="info-box-number">{{ total_lotteries }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">قرعات في الانتظار</span>
                                    <span class="info-box-number">{{ pending_lotteries }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check-circle"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">قرعات مكتملة</span>
                                    <span class="info-box-number">{{ completed_lotteries }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلترة -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-5">
                                <select name="camp_id" class="form-control">
                                    <option value="">جميع المخيمات</option>
                                    {% for camp in camps %}
                                    <option value="{{ camp.id }}" {% if camp_id == camp.id %}selected{% endif %}>
                                        {{ camp.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select name="status" class="form-control">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>في الانتظار</option>
                                    <option value="completed" {% if status == 'completed' %}selected{% endif %}>مكتملة</option>
                                    <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ url_for('main.vacation_lotteries_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> مسح
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- الجدول -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>اسم القرعة</th>
                                    <th>المخيم</th>
                                    <th>تاريخ القرعة</th>
                                    <th>فترة الإقامة</th>
                                    <th>المشاركون</th>
                                    <th>الفائزون</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for lottery in lotteries.items %}
                                <tr>
                                    <td>
                                        <strong>{{ lottery.name }}</strong>
                                        {% if lottery.description %}
                                        <br><small class="text-muted">{{ lottery.description[:50] }}...</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ lottery.camp.name }}</td>
                                    <td>{{ lottery.lottery_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <small>
                                            من {{ lottery.start_date.strftime('%Y-%m-%d') }}<br>
                                            إلى {{ lottery.end_date.strftime('%Y-%m-%d') }}<br>
                                            <span class="badge badge-info">{{ lottery.duration_days }} يوم</span>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">{{ lottery.participants_count }}</span>
                                        {% if lottery.max_participants %}
                                        / {{ lottery.max_participants }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-success">{{ lottery.winners_count }}</span>
                                    </td>
                                    <td>
                                        {% if lottery.status == 'pending' %}
                                            <span class="badge badge-warning">{{ lottery.status_display }}</span>
                                        {% elif lottery.status == 'completed' %}
                                            <span class="badge badge-success">{{ lottery.status_display }}</span>
                                        {% elif lottery.status == 'cancelled' %}
                                            <span class="badge badge-danger">{{ lottery.status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('main.vacation_lottery_detail', id=lottery.id) }}" 
                                               class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if lottery.status == 'pending' %}
                                            <button class="btn btn-sm btn-success" onclick="executeLottery({{ lottery.id }})" title="تنفيذ القرعة">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="cancelLottery({{ lottery.id }})" title="إلغاء القرعة">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <br>لا توجد قرعات
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- التصفح -->
                    {% if lotteries.pages > 1 %}
                    <nav aria-label="تصفح الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if lotteries.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_lotteries_list', page=lotteries.prev_num, camp_id=camp_id, status=status) }}">السابق</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in lotteries.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != lotteries.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.vacation_lotteries_list', page=page_num, camp_id=camp_id, status=status) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if lotteries.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.vacation_lotteries_list', page=lotteries.next_num, camp_id=camp_id, status=status) }}">التالي</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function executeLottery(lotteryId) {
    if (confirm('هل أنت متأكد من تنفيذ القرعة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/vacation_lottery/${lotteryId}/execute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function cancelLottery(lotteryId) {
    if (confirm('هل أنت متأكد من إلغاء القرعة؟')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = `/vacation_lottery/${lotteryId}/cancel`;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
