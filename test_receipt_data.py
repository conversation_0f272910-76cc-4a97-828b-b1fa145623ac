#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بيانات الوصل والعامل
"""

from app import create_app
from app.models import ThermalBathReceipt, Worker

def test_receipt_data():
    """اختبار بيانات الوصل والعامل"""
    app = create_app()
    
    with app.app_context():
        # الحصول على الوصل الأول
        receipt = ThermalBathReceipt.query.first()
        
        if receipt:
            print("=== بيانات الوصل ===")
            print(f"رقم الوصل: {receipt.receipt_number}")
            print(f"تاريخ الإصدار: {receipt.issue_date}")
            print(f"المبلغ الإجمالي: {receipt.total_amount}")
            print(f"مساهمة اللجنة: {receipt.committee_contribution}")
            print(f"دفع العامل: {receipt.worker_payment}")
            print(f"مكان العلاج: {receipt.treatment_location}")
            
            print("\n=== بيانات العامل ===")
            if receipt.worker:
                worker = receipt.worker
                print(f"الاسم الأول: {worker.first_name}")
                print(f"اللقب: {worker.last_name}")
                print(f"الاسم الكامل: {worker.full_name}")
                print(f"الهاتف: {worker.phone}")
                print(f"المنصب: {worker.position}")
                print(f"مكان العمل: {worker.workplace}")
                print(f"العنوان: {worker.personal_address}")
                print(f"تاريخ الميلاد: {worker.birth_date}")
                print(f"مكان الميلاد: {worker.birth_place}")
                print(f"الجنس: {worker.gender}")
                print(f"الحالة الاجتماعية: {worker.marital_status}")
            else:
                print("لا يوجد عامل مرتبط بهذا الوصل!")
                
        else:
            print("لا توجد وصولات في قاعدة البيانات!")
            
        # عرض جميع العمال
        print("\n=== جميع العمال ===")
        workers = Worker.query.all()
        for worker in workers:
            print(f"ID: {worker.id}, الاسم: {worker.full_name}, الهاتف: {worker.phone}")

if __name__ == "__main__":
    test_receipt_data()
