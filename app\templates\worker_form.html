{% extends "base.html" %}

{% block title %}
{% if worker %}تعديل العامل{% else %}إضافة عامل جديد{% endif %} - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-{% if worker %}edit{% else %}plus{% endif %} me-2"></i>
                    {% if worker %}تعديل بيانات العامل{% else %}إضافة عامل جديد{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- البيانات الشخصية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user me-1"></i>البيانات الشخصية
                            </h6>
                            
                            <div class="mb-3">
                                {{ form.first_name.label(class="form-label") }}
                                {{ form.first_name(class="form-control") }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.first_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.last_name.label(class="form-label") }}
                                {{ form.last_name(class="form-control") }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.last_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.birth_date.label(class="form-label") }}
                                {{ form.birth_date(class="form-control") }}
                                {% if form.birth_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.birth_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.birth_place.label(class="form-label") }}
                                {{ form.birth_place(class="form-control") }}
                                {% if form.birth_place.errors %}
                                    <div class="text-danger">
                                        {% for error in form.birth_place.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.gender.label(class="form-label") }}
                                {{ form.gender(class="form-select") }}
                                {% if form.gender.errors %}
                                    <div class="text-danger">
                                        {% for error in form.gender.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.marital_status.label(class="form-label") }}
                                {{ form.marital_status(class="form-select") }}
                                {% if form.marital_status.errors %}
                                    <div class="text-danger">
                                        {% for error in form.marital_status.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- معلومات الحساب البنكي/البريدي -->
                            <h6 class="text-success mb-3 mt-4">
                                <i class="fas fa-university me-1"></i>معلومات الحساب البنكي/البريدي
                            </h6>

                            <div class="mb-3">
                                {{ form.account_type.label(class="form-label") }}
                                {{ form.account_type(class="form-select", id="account_type") }}
                                {% if form.account_type.errors %}
                                    <div class="text-danger">
                                        {% for error in form.account_type.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                {{ form.account_number.label(class="form-label") }}
                                {{ form.account_number(class="form-control", maxlength="20", pattern="[0-9]{20}", placeholder="أدخل 20 رقم") }}
                                {% if form.account_number.errors %}
                                    <div class="text-danger">
                                        {% for error in form.account_number.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="text-muted">يجب أن يكون الرقم مكون من 20 رقم بالضبط</small>
                            </div>

                            <div class="mb-3" id="bank_fields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.bank_name.label(class="form-label") }}
                                        {{ form.bank_name(class="form-select", id="bank_name_select") }}
                                        {% if form.bank_name.errors %}
                                            <div class="text-danger">
                                                {% for error in form.bank_name.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}

                                        <!-- حقل نص مخصص للبنك -->
                                        <div id="custom_bank_field" style="display: none;" class="mt-2">
                                            <input type="text" id="custom_bank_name" class="form-control"
                                                   placeholder="أدخل اسم البنك..." maxlength="100">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.bank_agency.label(class="form-label") }}
                                        {{ form.bank_agency(class="form-control") }}
                                        {% if form.bank_agency.errors %}
                                            <div class="text-danger">
                                                {% for error in form.bank_agency.errors %}
                                                    <small>{{ error }}</small>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- بيانات العمل -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-briefcase me-1"></i>بيانات العمل
                            </h6>
                            
                            <div class="mb-3">
                                {{ form.personal_address.label(class="form-label") }}
                                {{ form.personal_address(class="form-control") }}
                                {% if form.personal_address.errors %}
                                    <div class="text-danger">
                                        {% for error in form.personal_address.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                {{ form.phone.label(class="form-label") }}
                                {{ form.phone(class="form-control", placeholder="مثال: 0555123456") }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">
                                        {% for error in form.phone.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.position.label(class="form-label") }}
                                {{ form.position(class="form-control") }}
                                {% if form.position.errors %}
                                    <div class="text-danger">
                                        {% for error in form.position.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.workplace.label(class="form-label") }}
                                {{ form.workplace(class="form-control") }}
                                {% if form.workplace.errors %}
                                    <div class="text-danger">
                                        {% for error in form.workplace.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <!-- صورة العامل -->
                            <div class="mb-3">
                                {{ form.photo.label(class="form-label") }}
                                {{ form.photo(class="form-control") }}
                                {% if form.photo.errors %}
                                    <div class="text-danger">
                                        {% for error in form.photo.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if worker and worker.photo_filename %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename='uploads/' + worker.photo_filename) }}" 
                                             alt="صورة العامل" class="img-thumbnail" style="max-width: 150px;">
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- بيانات الأطفال -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-child me-1"></i>بيانات الأطفال
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="addChild()">
                                    <i class="fas fa-plus"></i> إضافة طفل
                                </button>
                            </h6>
                            
                            <div id="children-container">
                                {% for child_form in form.children %}
                                    <div class="child-form border p-3 mb-3 rounded">
                                        {{ child_form.hidden_tag() }}
                                        <div class="row">
                                            <div class="col-md-3">
                                                <label class="form-label">اسم الطفل</label>
                                                {{ child_form.name(class="form-control") }}
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">تاريخ الميلاد</label>
                                                {{ child_form.birth_date(class="form-control") }}
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">الجنس</label>
                                                {{ child_form.gender(class="form-select") }}
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">المستوى الدراسي</label>
                                                {{ child_form.education_level(class="form-select") }}
                                            </div>
                                            <div class="col-md-1 d-flex align-items-end">
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeChild(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('main.workers_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>إلغاء
                                </a>
                                {{ form.submit(class="btn btn-primary") }}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const accountTypeSelect = document.getElementById('account_type');
    const bankFields = document.getElementById('bank_fields');
    const bankNameSelect = document.getElementById('bank_name_select');
    const customBankField = document.getElementById('custom_bank_field');
    const customBankInput = document.getElementById('custom_bank_name');
    const bankAgencyField = document.querySelector('input[name="bank_agency"]');

    function toggleBankFields() {
        if (accountTypeSelect.value === 'بنكي') {
            bankFields.style.display = 'block';
            bankNameSelect.required = true;
            bankAgencyField.required = true;
        } else {
            bankFields.style.display = 'none';
            bankNameSelect.required = false;
            bankAgencyField.required = false;
            bankNameSelect.value = '';
            bankAgencyField.value = '';
            customBankField.style.display = 'none';
            customBankInput.value = '';
        }
    }

    function toggleCustomBankField() {
        if (bankNameSelect.value === 'أخرى') {
            customBankField.style.display = 'block';
            customBankInput.required = true;
        } else {
            customBankField.style.display = 'none';
            customBankInput.required = false;
            customBankInput.value = '';
        }
    }

    // تشغيل الدالة عند تحميل الصفحة
    toggleBankFields();
    toggleCustomBankField();

    // تشغيل الدالة عند تغيير نوع الحساب
    accountTypeSelect.addEventListener('change', toggleBankFields);

    // تشغيل الدالة عند تغيير اسم البنك
    bankNameSelect.addEventListener('change', toggleCustomBankField);

    // عند إرسال النموذج، استخدم القيمة المخصصة إذا تم اختيار "أخرى"
    const form = document.querySelector('form');
    form.addEventListener('submit', function() {
        if (bankNameSelect.value === 'أخرى' && customBankInput.value.trim()) {
            // إنشاء حقل مخفي بالقيمة المخصصة
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'bank_name';
            hiddenInput.value = customBankInput.value.trim();
            form.appendChild(hiddenInput);

            // إزالة القيمة من القائمة المنسدلة لتجنب التضارب
            bankNameSelect.name = 'bank_name_select_temp';
        }
    });

    // التحقق من صحة رقم الحساب (20 رقم)
    const accountNumberField = document.querySelector('input[name="account_number"]');
    if (accountNumberField) {
        accountNumberField.addEventListener('input', function() {
            // إزالة أي حروف غير رقمية
            this.value = this.value.replace(/[^0-9]/g, '');

            // تحديد الطول إلى 20 رقم
            if (this.value.length > 20) {
                this.value = this.value.substring(0, 20);
            }
        });
    }
});
</script>
{% endblock %}

{% block scripts %}
<script>
let childIndex = {{ form.children|length if form.children else 0 }};

function addChild() {
    const container = document.getElementById('children-container');
    const childForm = `
        <div class="child-form border p-3 mb-3 rounded">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">اسم الطفل</label>
                    <input type="text" name="children-${childIndex}-name" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">تاريخ الميلاد</label>
                    <input type="date" name="children-${childIndex}-birth_date" class="form-control" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الجنس</label>
                    <select name="children-${childIndex}-gender" class="form-select" required>
                        <option value="">اختر...</option>
                        <option value="ذكر">ذكر</option>
                        <option value="أنثى">أنثى</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">المستوى الدراسي</label>
                    <select name="children-${childIndex}-education_level" class="form-select" required>
                        <option value="">اختر...</option>
                        <option value="ابتدائي">ابتدائي</option>
                        <option value="متوسط">متوسط</option>
                        <option value="ثانوي">ثانوي</option>
                        <option value="جامعي">جامعي</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeChild(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', childForm);
    childIndex++;
}

function removeChild(button) {
    button.closest('.child-form').remove();
}
</script>
{% endblock %}
