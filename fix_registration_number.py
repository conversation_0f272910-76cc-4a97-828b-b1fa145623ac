#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح حقل رقم التسجيل
"""

import sqlite3
import os

def add_registration_number():
    """إضافة حقل رقم التسجيل بدون قيد UNIQUE"""
    db_path = 'instance/workers_committee.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # التحقق من وجود الحقل
        cursor.execute("PRAGMA table_info(workers)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'registration_number' not in columns:
            # إضافة الحقل بدون قيد UNIQUE
            cursor.execute("ALTER TABLE workers ADD COLUMN registration_number VARCHAR(20)")
            print("✅ تم إضافة حقل registration_number")
        else:
            print("ℹ️ حقل registration_number موجود مسبقاً")
        
        # توليد أرقام تسجيل للعمال الموجودين
        cursor.execute("SELECT id, first_name, last_name FROM workers WHERE registration_number IS NULL OR registration_number = ''")
        workers = cursor.fetchall()
        
        if workers:
            print(f"🔢 توليد أرقام تسجيل لـ {len(workers)} عامل...")
            
            for i, (worker_id, first_name, last_name) in enumerate(workers, 1):
                reg_number = f"EMP{i:04d}"
                cursor.execute("UPDATE workers SET registration_number = ? WHERE id = ?", (reg_number, worker_id))
                print(f"✅ {last_name} {first_name}: {reg_number}")
        
        conn.commit()
        print("✅ تم إضافة أرقام التسجيل بنجاح!")
        
        # عرض النتيجة
        cursor.execute("SELECT registration_number, first_name, last_name FROM workers")
        all_workers = cursor.fetchall()
        
        print("\n📝 أرقام التسجيل النهائية:")
        print("-" * 40)
        for reg_num, first_name, last_name in all_workers:
            print(f"{reg_num}: {last_name} {first_name}")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ خطأ: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    add_registration_number()
